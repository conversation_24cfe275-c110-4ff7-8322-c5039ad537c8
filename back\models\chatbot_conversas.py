"""
Modelo para armazenar conversas do chatbot IA
"""

from .escritorio import db
from sqlalchemy.sql import func
import json

class ChatbotConversas(db.Model):
    __tablename__ = 'chatbot_conversas'
    
    id = db.Column(db.Integer, primary_key=True)
    usuario_id = db.<PERSON>umn(db.<PERSON>, db.<PERSON><PERSON>('usuario.id'), nullable=False)
    empresa_id = db.<PERSON>umn(db.<PERSON><PERSON>, db.<PERSON><PERSON>('empresa.id'), nullable=True)
    escritorio_id = db.Column(db.Integer, db.<PERSON><PERSON>('escritorio.id'), nullable=True)
    pergunta = db.Column(db.Text, nullable=False)
    resposta = db.Column(db.Text, nullable=False)
    contexto_sql = db.Column(db.Text, nullable=True)
    dados_utilizados = db.Column(db.<PERSON><PERSON><PERSON>, nullable=True)
    tempo_resposta = db.Column(db.Integer, nullable=True)  # em milissegundos
    data_criacao = db.Column(db.DateTime, server_default=func.now())
    avaliacao = db.Column(db.Integer, nullable=True)  # 1-5
    feedback = db.Column(db.Text, nullable=True)
    
    # Relacionamentos
    usuario = db.relationship('Usuario', backref='chatbot_conversas')
    empresa = db.relationship('Empresa', backref='chatbot_conversas')
    escritorio = db.relationship('Escritorio', backref='chatbot_conversas')
    
    def to_dict(self):
        """Converte o objeto para dicionário"""
        return {
            'id': self.id,
            'usuario_id': self.usuario_id,
            'empresa_id': self.empresa_id,
            'escritorio_id': self.escritorio_id,
            'pergunta': self.pergunta,
            'resposta': self.resposta,
            'contexto_sql': self.contexto_sql,
            'dados_utilizados': self.dados_utilizados,
            'tempo_resposta': self.tempo_resposta,
            'data_criacao': self.data_criacao.isoformat() if self.data_criacao else None,
            'avaliacao': self.avaliacao,
            'feedback': self.feedback
        }
    
    def __repr__(self):
        return f'<ChatbotConversas {self.id}: {self.pergunta[:50]}...>'
