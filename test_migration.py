#!/usr/bin/env python3
"""
Script para testar se a migração foi aplicada
"""

import sys
import os
sys.path.append('back')

from models import db, AuditoriaResultado
from sqlalchemy import inspect

def test_migration():
    """Testa se a coluna cenario_status foi criada"""
    try:
        # Verificar se a coluna existe no modelo
        if hasattr(AuditoriaResultado, 'cenario_status'):
            print("✅ Coluna 'cenario_status' existe no modelo AuditoriaResultado")
        else:
            print("❌ Coluna 'cenario_status' NÃO existe no modelo AuditoriaResultado")
            return False
        
        # Verificar se a coluna existe na tabela do banco
        inspector = inspect(db.engine)
        columns = inspector.get_columns('auditoria_resultado')
        column_names = [col['name'] for col in columns]
        
        if 'cenario_status' in column_names:
            print("✅ Coluna 'cenario_status' existe na tabela do banco de dados")
        else:
            print("❌ Coluna 'cenario_status' NÃO existe na tabela do banco de dados")
            print("Execute a migração: db/migration_add_cenario_status.sql")
            return False
        
        # Testar uma consulta simples
        resultado = AuditoriaResultado.query.first()
        if resultado:
            print(f"✅ Teste de consulta bem-sucedido. cenario_status = {resultado.cenario_status}")
        else:
            print("⚠️  Nenhum resultado de auditoria encontrado para testar")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro ao testar migração: {e}")
        return False

if __name__ == '__main__':
    from app import create_app
    app = create_app()
    
    with app.app_context():
        success = test_migration()
        if success:
            print("\n🎉 Migração aplicada com sucesso!")
        else:
            print("\n💥 Problemas encontrados na migração!")
