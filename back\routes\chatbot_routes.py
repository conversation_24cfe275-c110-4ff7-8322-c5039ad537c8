"""
Rotas para o Chatbot IA do Sistema de Auditoria Fiscal
"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from models import Usuario
from services.chatbot_ia_service import ChatbotIAService
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Criar blueprint
chatbot_bp = Blueprint('chatbot', __name__)

# Instanciar serviço de IA
chatbot_service = ChatbotIAService()

@chatbot_bp.route('/chatbot/pergunta', methods=['POST'])
@jwt_required()
def processar_pergunta():
    """
    Processa uma pergunta do usuário e retorna resposta da IA
    """
    try:
        # Obter dados da requisição
        data = request.get_json()
        pergunta = data.get('pergunta', '').strip()
        empresa_id = data.get('empresa_id')

        if not pergunta:
            return jsonify({
                'success': False,
                'message': 'Pergunta não pode estar vazia'
            }), 400

        # Obter usuário atual
        current_user_id = get_jwt_identity()
        usuario = Usuario.query.get(current_user_id)

        if not usuario:
            return jsonify({
                'success': False,
                'message': 'Usuário não encontrado'
            }), 404

        # Processar pergunta com IA
        resultado = chatbot_service.processar_pergunta(
            pergunta=pergunta,
            usuario_id=current_user_id,
            empresa_id=empresa_id
        )

        return jsonify(resultado)

    except Exception as e:
        logger.error(f"Erro ao processar pergunta: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'Erro interno do servidor',
            'erro': str(e)
        }), 500

@chatbot_bp.route('/chatbot/historico', methods=['GET'])
@jwt_required()
def obter_historico():
    """
    Obtém o histórico de conversas do usuário
    """
    try:
        current_user_id = get_jwt_identity()
        limite = request.args.get('limite', 20, type=int)

        historico = chatbot_service.obter_historico(
            usuario_id=current_user_id,
            limite=limite
        )

        return jsonify({
            'success': True,
            'historico': historico
        })

    except Exception as e:
        logger.error(f"Erro ao obter histórico: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'Erro ao obter histórico'
        }), 500

@chatbot_bp.route('/chatbot/avaliar', methods=['POST'])
@jwt_required()
def avaliar_resposta():
    """
    Permite ao usuário avaliar uma resposta do chatbot
    """
    try:
        data = request.get_json()
        conversa_id = data.get('conversa_id')
        avaliacao = data.get('avaliacao')  # 1-5
        feedback = data.get('feedback', '')

        if not conversa_id or not avaliacao:
            return jsonify({
                'success': False,
                'message': 'ID da conversa e avaliação são obrigatórios'
            }), 400

        if avaliacao < 1 or avaliacao > 5:
            return jsonify({
                'success': False,
                'message': 'Avaliação deve ser entre 1 e 5'
            }), 400

        sucesso = chatbot_service.avaliar_resposta(
            conversa_id=conversa_id,
            avaliacao=avaliacao,
            feedback=feedback
        )

        if sucesso:
            return jsonify({
                'success': True,
                'message': 'Avaliação salva com sucesso'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Erro ao salvar avaliação'
            }), 400

    except Exception as e:
        logger.error(f"Erro ao avaliar resposta: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'Erro ao salvar avaliação'
        }), 500

@chatbot_bp.route('/chatbot/sugestoes', methods=['GET'])
@jwt_required()
def obter_sugestoes():
    """
    Retorna sugestões de perguntas para o usuário baseadas no contexto
    """
    try:
        # Obter parâmetros
        empresa_id = request.args.get('empresa_id', type=int)
        current_user_id = get_jwt_identity()
        usuario = Usuario.query.get(current_user_id)

        if not usuario:
            return jsonify({
                'success': False,
                'message': 'Usuário não encontrado'
            }), 404

        # Sugestões baseadas no contexto da empresa
        sugestoes_contextuais = []

        if empresa_id:
            # Buscar dados da empresa para sugestões contextuais
            from models import Empresa, NotaFiscalItem, AuditoriaResultado
            from sqlalchemy import func, desc

            empresa = Empresa.query.get(empresa_id)
            if empresa:
                # Verificar se há notas fiscais
                total_notas = NotaFiscalItem.query.filter_by(empresa_id=empresa_id).count()
                if total_notas > 0:
                    sugestoes_contextuais.extend([
                        f"Quantas notas fiscais a empresa {empresa.razao_social} possui?",
                        f"Qual o valor total das operações da empresa {empresa.razao_social}?",
                        "Quais são os produtos mais vendidos?",
                        "Quais clientes mais compram?"
                    ])

                # Verificar se há auditorias realizadas
                total_auditorias = AuditoriaResultado.query.filter_by(empresa_id=empresa_id).count()
                if total_auditorias > 0:
                    sugestoes_contextuais.extend([
                        "Quantas inconsistências foram encontradas na auditoria?",
                        "Qual tributo tem mais problemas?",
                        "Qual o percentual de conformidade da empresa?",
                        "Quais são os principais tipos de inconsistências?"
                    ])

                # Buscar nota mais recente para sugestão específica
                nota_recente = NotaFiscalItem.query.filter_by(empresa_id=empresa_id)\
                    .order_by(desc(NotaFiscalItem.data_emissao)).first()
                if nota_recente:
                    sugestoes_contextuais.append(f"Me fale sobre a nota {nota_recente.numero_nf}")

        # Sugestões gerais sempre disponíveis
        sugestoes_gerais = [
            "Qual o resumo geral das auditorias?",
            "Quantas empresas estão cadastradas?",
            "Quais são as inconsistências mais comuns?",
            "Como está a conformidade fiscal geral?",
            "Quantos produtos estão cadastrados?",
            "Qual o período das notas importadas?",
            "Quais tributos são mais auditados?",
            "Há problemas de CST nas auditorias?",
            "Qual a diferença média entre nota e cálculo?",
            "Quantos cenários estão em produção?"
        ]

        # Sugestões específicas por tipo de consulta
        sugestoes_especificas = [
            # Consultas sobre notas específicas
            "Me mostre os dados da nota 12345",
            "Qual o CFOP mais utilizado?",
            "Quais NCMs são mais comuns?",

            # Consultas sobre inconsistências
            "Quais notas têm problemas de ICMS?",
            "Há inconsistências de alíquota?",
            "Quais produtos têm mais problemas tributários?",

            # Consultas temporais
            "Quantas notas foram emitidas em dezembro de 2024?",
            "Qual o valor total das operações em 2024?",
            "Como foi a conformidade no último mês?",

            # Consultas por tributo
            "Qual o status da auditoria de IPI?",
            "Há problemas com PIS/COFINS?",
            "Como está o ICMS-ST?",

            # Consultas analíticas
            "Qual cliente tem mais inconsistências?",
            "Qual produto gera mais problemas fiscais?",
            "Quais são os CFOPs com mais erros?"
        ]

        # Combinar sugestões (priorizar contextuais)
        sugestoes = sugestoes_contextuais[:3] + sugestoes_gerais[:4] + sugestoes_especificas[:3]

        # Embaralhar para variedade
        import random
        random.shuffle(sugestoes)

        # Limitar a 8 sugestões
        sugestoes = sugestoes[:8]

        return jsonify({
            'success': True,
            'sugestoes': sugestoes
        })

    except Exception as e:
        logger.error(f"Erro ao obter sugestões: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'Erro ao obter sugestões'
        }), 500

@chatbot_bp.route('/chatbot/status', methods=['GET'])
@jwt_required()
def verificar_status():
    """
    Verifica se o serviço de IA está funcionando
    """
    try:
        # Teste simples para verificar se a IA está respondendo
        resultado = chatbot_service.processar_pergunta(
            pergunta="teste",
            usuario_id=get_jwt_identity(),
            empresa_id=None
        )

        return jsonify({
            'success': True,
            'status': 'online',
            'message': 'Chatbot IA funcionando normalmente'
        })

    except Exception as e:
        logger.error(f"Erro ao verificar status: {str(e)}")
        return jsonify({
            'success': False,
            'status': 'offline',
            'message': 'Chatbot IA indisponível',
            'erro': str(e)
        }), 500
