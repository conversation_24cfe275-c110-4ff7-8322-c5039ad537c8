"""
Rotas para o Chatbot IA do Sistema de Auditoria Fiscal
"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from models import Usuario
from services.chatbot_ia_service import ChatbotIAService
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Criar blueprint
chatbot_bp = Blueprint('chatbot', __name__)

# Instanciar serviço de IA
chatbot_service = ChatbotIAService()

@chatbot_bp.route('/chatbot/pergunta', methods=['POST'])
@jwt_required()
def processar_pergunta():
    """
    Processa uma pergunta do usuário e retorna resposta da IA
    """
    try:
        # Obter dados da requisição
        data = request.get_json()
        pergunta = data.get('pergunta', '').strip()
        empresa_id = data.get('empresa_id')
        
        if not pergunta:
            return jsonify({
                'success': False,
                'message': 'Pergunta não pode estar vazia'
            }), 400
        
        # Obter usuário atual
        current_user_id = get_jwt_identity()
        usuario = Usuario.query.get(current_user_id)
        
        if not usuario:
            return jsonify({
                'success': False,
                'message': 'Usuário não encontrado'
            }), 404
        
        # Processar pergunta com IA
        resultado = chatbot_service.processar_pergunta(
            pergunta=pergunta,
            usuario_id=current_user_id,
            empresa_id=empresa_id
        )
        
        return jsonify(resultado)
        
    except Exception as e:
        logger.error(f"Erro ao processar pergunta: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'Erro interno do servidor',
            'erro': str(e)
        }), 500

@chatbot_bp.route('/chatbot/historico', methods=['GET'])
@jwt_required()
def obter_historico():
    """
    Obtém o histórico de conversas do usuário
    """
    try:
        current_user_id = get_jwt_identity()
        limite = request.args.get('limite', 20, type=int)
        
        historico = chatbot_service.obter_historico(
            usuario_id=current_user_id,
            limite=limite
        )
        
        return jsonify({
            'success': True,
            'historico': historico
        })
        
    except Exception as e:
        logger.error(f"Erro ao obter histórico: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'Erro ao obter histórico'
        }), 500

@chatbot_bp.route('/chatbot/avaliar', methods=['POST'])
@jwt_required()
def avaliar_resposta():
    """
    Permite ao usuário avaliar uma resposta do chatbot
    """
    try:
        data = request.get_json()
        conversa_id = data.get('conversa_id')
        avaliacao = data.get('avaliacao')  # 1-5
        feedback = data.get('feedback', '')
        
        if not conversa_id or not avaliacao:
            return jsonify({
                'success': False,
                'message': 'ID da conversa e avaliação são obrigatórios'
            }), 400
        
        if avaliacao < 1 or avaliacao > 5:
            return jsonify({
                'success': False,
                'message': 'Avaliação deve ser entre 1 e 5'
            }), 400
        
        sucesso = chatbot_service.avaliar_resposta(
            conversa_id=conversa_id,
            avaliacao=avaliacao,
            feedback=feedback
        )
        
        if sucesso:
            return jsonify({
                'success': True,
                'message': 'Avaliação salva com sucesso'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Erro ao salvar avaliação'
            }), 400
        
    except Exception as e:
        logger.error(f"Erro ao avaliar resposta: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'Erro ao salvar avaliação'
        }), 500

@chatbot_bp.route('/chatbot/sugestoes', methods=['GET'])
@jwt_required()
def obter_sugestoes():
    """
    Retorna sugestões de perguntas para o usuário
    """
    try:
        sugestoes = [
        ]
        
        return jsonify({
            'success': True,
            'sugestoes': sugestoes
        })
        
    except Exception as e:
        logger.error(f"Erro ao obter sugestões: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'Erro ao obter sugestões'
        }), 500

@chatbot_bp.route('/chatbot/status', methods=['GET'])
@jwt_required()
def verificar_status():
    """
    Verifica se o serviço de IA está funcionando
    """
    try:
        # Teste simples para verificar se a IA está respondendo
        resultado = chatbot_service.processar_pergunta(
            pergunta="teste",
            usuario_id=get_jwt_identity(),
            empresa_id=None
        )
        
        return jsonify({
            'success': True,
            'status': 'online',
            'message': 'Chatbot IA funcionando normalmente'
        })
        
    except Exception as e:
        logger.error(f"Erro ao verificar status: {str(e)}")
        return jsonify({
            'success': False,
            'status': 'offline',
            'message': 'Chatbot IA indisponível',
            'erro': str(e)
        }), 500
