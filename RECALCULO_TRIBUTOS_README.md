# Correção do Recálculo de Tributos

## Problema Identificado

O sistema não estava recalculando corretamente os valores dos tributos quando campos críticos dos cenários eram alterados. Especificamente:

1. **M<PERSON><PERSON><PERSON> `_calculate_*` incompletos**: Os métodos no `TributoCalculationService` calculavam valores mas não atualizavam as colunas `cenario_*_vbc` e `cenario_*_valor` na tabela `tributo`.

2. **Falta de validação de CST**: Alguns métodos não verificavam se o CST permitia tributação antes de calcular.

3. **Inconsistência entre métodos**: Existiam métodos estáticos que funcionavam corretamente, mas os métodos de instância não faziam a atualização completa.

## Exemplo do Problema

Quando um cenário tinha CST 04 (não tributado) e era alterado para CST 00 (tributado), os valores `cenario_icms_vbc` e `cenario_icms_valor` permaneciam vazios, mesmo com alíquota definida.

## Soluções Implementadas

### 1. Correção dos Métodos `_calculate_*`

**Arquivo**: `back/services/tributo_calculation_service.py`

Todos os métodos `_calculate_icms`, `_calculate_icms_st`, `_calculate_ipi`, `_calculate_pis`, `_calculate_cofins` e `_calculate_difal` foram corrigidos para:

- Usar os métodos estáticos existentes para garantir consistência
- Verificar CST adequadamente antes de calcular
- Atualizar as colunas `cenario_*_vbc`, `cenario_*_valor` e `cenario_*_id` na tabela `tributo`
- Incluir tratamento de erros e logs detalhados

**Exemplo da correção (ICMS)**:
```python
def _calculate_icms(self, tributo, cenario):
    """Calcula ICMS e atualiza as colunas cenario_icms_* na tabela tributo"""
    try:
        # Usar o método estático para garantir consistência
        valor_total = Decimal(str(tributo.valor_total)) if tributo.valor_total else Decimal('0.00')
        # ... outros parâmetros ...
        
        base_calculo, valor_icms = self.calcular_icms(
            valor_total, cenario, valor_ipi, cliente_uso_consumo_ativo, valor_frete, valor_desconto
        )

        # Atualizar valores no tributo
        tributo.cenario_icms_id = cenario.id
        tributo.cenario_icms_vbc = float(base_calculo)
        tributo.cenario_icms_valor = float(valor_icms)

        return True
    except Exception as e:
        logger.error(f"Erro ao calcular ICMS para tributo {tributo.id}: {str(e)}")
        return False
```

### 2. Implementação Completa do DIFAL

O método `calcular_difal` estava incompleto. Foi implementado com a lógica completa:

```python
@staticmethod
def calcular_difal(valor_total, cenario_difal):
    # Verificar campos necessários
    if (not cenario_difal.p_icms_uf_dest or not cenario_difal.p_icms_inter or not valor_total):
        return Decimal('0.00')

    # Calcular base de cálculo com redução se houver
    base_calculo = Decimal(str(valor_total))
    if cenario_difal.p_red_bc:
        base_calculo = base_calculo * (Decimal('1.00') - Decimal(str(cenario_difal.p_red_bc)) / Decimal('100.00'))

    # Calcular diferença entre alíquotas
    diferenca = Decimal(str(cenario_difal.p_icms_uf_dest)) - Decimal(str(cenario_difal.p_icms_inter))
    
    # Aplicar partilha se houver
    if cenario_difal.p_icms_inter_part:
        diferenca = diferenca * Decimal(str(cenario_difal.p_icms_inter_part)) / Decimal('100.00')

    # Calcular valor do DIFAL + FCP
    valor_difal = (base_calculo * diferenca) / Decimal('100.00')
    if cenario_difal.p_fcp_uf_dest:
        fcp = (base_calculo * Decimal(str(cenario_difal.p_fcp_uf_dest))) / Decimal('100.00')
        valor_difal += fcp

    return valor_difal
```

### 3. Melhoria na Lógica de Recálculo

**Arquivo**: `back/routes/cenario_routes.py`

A lógica de recálculo foi melhorada para:

- Recalcular apenas o tipo de tributo específico que foi alterado
- Buscar tributos relacionados ao produto e cliente do cenário
- Incluir tratamento de erros com rollback
- Adicionar logs mais detalhados

```python
# Se campos críticos foram alterados, recalcular tributos afetados
if campos_criticos and cenario.empresa_id and cenario.produto_id:
    try:
        calculation_service = TributoCalculationService(cenario.empresa_id)
        
        # Buscar tributos que usam este produto e cliente
        tributos = Tributo.query.filter_by(
            empresa_id=cenario.empresa_id,
            produto_id=cenario.produto_id,
            cliente_id=cenario.cliente_id
        ).all()
        
        # Recalcular apenas o tipo de tributo específico que foi alterado
        for tributo in tributos:
            calculation_service._calculate_tributo_values(tributo, tipo_tributo)
        
        db.session.commit()
        logger.info(f"Tributos {tipo_tributo} recalculados para {len(tributos)} registros")
    except Exception as e:
        logger.error(f"Erro ao recalcular tributos: {str(e)}")
        db.session.rollback()
```

### 4. Melhoria na Interface do Usuário

**Arquivo**: `front/static/js/cenario_modal_enhanced.js`

A interface foi melhorada para informar o usuário quando o recálculo é realizado:

```javascript
.then((data) => {
  if (data.success) {
    let message = 'Cenário atualizado com sucesso!';
    
    // Adicionar informação sobre recálculo se foi realizado
    if (data.recalculo_realizado) {
      message += `\n\nOs valores de ${data.tipo_tributo_recalculado.toUpperCase()} foram recalculados automaticamente devido às alterações em campos críticos.`;
    }
    
    alert(message);
    // ... resto do código ...
  }
})
```

## Campos Críticos por Tipo de Tributo

Os seguintes campos são considerados críticos e disparam o recálculo:

- **ICMS**: `cst`, `aliquota`, `p_red_bc`, `mod_bc`
- **ICMS-ST**: `cst`, `aliquota`, `p_red_bc`, `mod_bc`, `icms_st_aliquota`, `icms_st_p_mva`
- **IPI**: `cst`, `aliquota`
- **PIS**: `cst`, `aliquota`
- **COFINS**: `cst`, `aliquota`
- **DIFAL**: `cst`, `aliquota`, `p_fcp_uf_dest`, `p_icms_uf_dest`

## Como Testar

1. **Teste Manual**:
   - Acesse um cenário com CST 04 (não tributado)
   - Altere para CST 00 (tributado) e defina uma alíquota
   - Verifique se os valores `cenario_*_vbc` e `cenario_*_valor` são calculados

2. **Teste Automatizado**:
   ```bash
   python test_cenario_recalculo.py
   ```

## Validações de CST Implementadas

- **ICMS**: CSTs 00, 10, 20, 70 são tributados
- **ICMS-ST**: CSTs 10, 70 são tributados com ST
- **IPI**: CST 50 é tributado
- **PIS/COFINS**: CSTs 01, 02, 1, 2 são tributados
- **DIFAL**: Depende dos percentuais configurados

## Logs e Debug

O sistema agora inclui logs detalhados para facilitar o debug:

```
INFO - ICMS calculado para tributo 123: BC=1000.00, Valor=180.00
INFO - Tributos icms recalculados para 5 registros após atualização de cenário
```

## Impacto na Performance

- O recálculo é feito apenas quando campos críticos são alterados
- Apenas o tipo de tributo específico é recalculado (não todos os tipos)
- Busca otimizada por produto e cliente específicos

## Compatibilidade

As alterações são totalmente compatíveis com o sistema existente:
- Não quebram funcionalidades existentes
- Mantêm a mesma interface de API
- Preservam a estrutura do banco de dados
