#!/usr/bin/env python3
"""
Script de teste para verificar se o recálculo de tributos está funcionando
quando campos críticos de cenários são alterados.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'back'))

from models import db, Tributo, CenarioICMS, Empresa, Cliente, Produto
from services.tributo_calculation_service import TributoCalculationService
from decimal import Decimal

def test_icms_recalculo():
    """
    Testa se o recálculo de ICMS funciona quando CST é alterado de 04 para 00
    """
    print("=== Teste de Recálculo de ICMS ===")
    
    try:
        # Buscar um cenário de ICMS para teste
        cenario_icms = CenarioICMS.query.filter_by(status='novo').first()
        
        if not cenario_icms:
            print("❌ Nenhum cenário de ICMS encontrado para teste")
            return False
            
        print(f"📋 Cenário encontrado: ID {cenario_icms.id}")
        print(f"   - CST atual: {cenario_icms.cst}")
        print(f"   - Alíquota: {cenario_icms.aliquota}%")
        
        # Buscar tributos relacionados a este cenário
        tributos = Tributo.query.filter_by(
            empresa_id=cenario_icms.empresa_id,
            cliente_id=cenario_icms.cliente_id,
            produto_id=cenario_icms.produto_id
        ).all()
        
        if not tributos:
            print("❌ Nenhum tributo encontrado para este cenário")
            return False
            
        print(f"📊 {len(tributos)} tributo(s) encontrado(s)")
        
        # Verificar valores antes da alteração
        tributo = tributos[0]
        print(f"\n🔍 Valores ANTES da alteração (Tributo ID {tributo.id}):")
        print(f"   - cenario_icms_vbc: {tributo.cenario_icms_vbc}")
        print(f"   - cenario_icms_valor: {tributo.cenario_icms_valor}")
        print(f"   - cenario_icms_id: {tributo.cenario_icms_id}")
        
        # Simular alteração do CST para um valor que permite tributação
        cst_original = cenario_icms.cst
        cenario_icms.cst = '00'  # CST que permite tributação
        
        if not cenario_icms.aliquota:
            cenario_icms.aliquota = Decimal('18.00')  # Definir alíquota se não existir
            
        print(f"\n🔄 Alterando CST de '{cst_original}' para '{cenario_icms.cst}'")
        print(f"   - Alíquota definida: {cenario_icms.aliquota}%")
        
        # Executar o recálculo usando o serviço
        calculation_service = TributoCalculationService(cenario_icms.empresa_id)
        
        # Testar o método de cálculo específico
        resultado = calculation_service._calculate_icms(tributo, cenario_icms)
        
        print(f"\n✅ Resultado do cálculo: {resultado}")
        
        # Verificar valores após o cálculo
        print(f"\n🔍 Valores APÓS o cálculo:")
        print(f"   - cenario_icms_vbc: {tributo.cenario_icms_vbc}")
        print(f"   - cenario_icms_valor: {tributo.cenario_icms_valor}")
        print(f"   - cenario_icms_id: {tributo.cenario_icms_id}")
        
        # Verificar se os valores foram calculados corretamente
        if (tributo.cenario_icms_vbc is not None and 
            tributo.cenario_icms_valor is not None and 
            tributo.cenario_icms_id == cenario_icms.id):
            print("\n✅ SUCESSO: Valores foram recalculados corretamente!")
            
            # Calcular valores esperados manualmente para verificação
            valor_total = Decimal(str(tributo.valor_total)) if tributo.valor_total else Decimal('0.00')
            base_esperada = valor_total
            valor_esperado = (base_esperada * cenario_icms.aliquota) / Decimal('100.00')
            
            print(f"\n📊 Verificação dos cálculos:")
            print(f"   - Valor total: R$ {valor_total}")
            print(f"   - Base esperada: R$ {base_esperada}")
            print(f"   - Valor esperado: R$ {valor_esperado}")
            print(f"   - Base calculada: R$ {tributo.cenario_icms_vbc}")
            print(f"   - Valor calculado: R$ {tributo.cenario_icms_valor}")
            
            return True
        else:
            print("\n❌ FALHA: Valores não foram recalculados corretamente!")
            return False
            
    except Exception as e:
        print(f"\n❌ ERRO durante o teste: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # Reverter alterações para não afetar o banco
        if 'cst_original' in locals() and 'cenario_icms' in locals():
            cenario_icms.cst = cst_original
            print(f"\n🔄 CST revertido para '{cst_original}'")

def test_cst_validation():
    """
    Testa se a validação de CST está funcionando corretamente
    """
    print("\n=== Teste de Validação de CST ===")
    
    try:
        # Criar um cenário de teste temporário
        from models import CenarioICMS
        
        # Buscar dados existentes para usar como base
        cenario_existente = CenarioICMS.query.first()
        if not cenario_existente:
            print("❌ Nenhum cenário existente encontrado")
            return False
            
        # Testar CST que não permite tributação (04)
        print("🧪 Testando CST 04 (não tributado)...")
        base_calculo, valor_icms = TributoCalculationService.calcular_icms(
            Decimal('1000.00'),  # Valor total
            type('CenarioTest', (), {
                'cst': '04',
                'aliquota': Decimal('18.00'),
                'p_red_bc': None
            })()
        )
        
        print(f"   - Base de cálculo: R$ {base_calculo}")
        print(f"   - Valor ICMS: R$ {valor_icms}")
        
        if base_calculo == Decimal('0.00') and valor_icms == Decimal('0.00'):
            print("   ✅ CST 04 corretamente não tributado")
        else:
            print("   ❌ CST 04 deveria resultar em valores zerados")
            return False
            
        # Testar CST que permite tributação (00)
        print("\n🧪 Testando CST 00 (tributado)...")
        base_calculo, valor_icms = TributoCalculationService.calcular_icms(
            Decimal('1000.00'),  # Valor total
            type('CenarioTest', (), {
                'cst': '00',
                'aliquota': Decimal('18.00'),
                'p_red_bc': None
            })()
        )
        
        print(f"   - Base de cálculo: R$ {base_calculo}")
        print(f"   - Valor ICMS: R$ {valor_icms}")
        
        if base_calculo == Decimal('1000.00') and valor_icms == Decimal('180.00'):
            print("   ✅ CST 00 corretamente tributado")
            return True
        else:
            print("   ❌ CST 00 deveria resultar em BC=1000 e Valor=180")
            return False
            
    except Exception as e:
        print(f"\n❌ ERRO durante o teste: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Iniciando testes de recálculo de tributos...\n")
    
    # Executar testes
    teste1 = test_cst_validation()
    teste2 = test_icms_recalculo()
    
    print(f"\n📋 Resumo dos testes:")
    print(f"   - Validação de CST: {'✅ PASSOU' if teste1 else '❌ FALHOU'}")
    print(f"   - Recálculo de ICMS: {'✅ PASSOU' if teste2 else '❌ FALHOU'}")
    
    if teste1 and teste2:
        print("\n🎉 Todos os testes passaram! O sistema está funcionando corretamente.")
        sys.exit(0)
    else:
        print("\n⚠️  Alguns testes falharam. Verifique as implementações.")
        sys.exit(1)
