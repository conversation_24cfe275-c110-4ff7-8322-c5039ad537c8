"""
Serviço de IA para Chatbot do Sistema de Auditoria Fiscal
Integração com OpenAI GPT-4o-mini para responder perguntas sobre auditorias
"""

import os
import json
import re
import time
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import openai
from sqlalchemy import text
from models import db
from models.chatbot_conversas import ChatbotConversas
from models.chatbot_templates import ChatbotTemplates

class ChatbotIAService:
    def __init__(self):
        self.client = openai.OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        self.model = os.getenv('OPENAI_MODEL', 'gpt-4o-mini')
        self.max_tokens = int(os.getenv('OPENAI_MAX_TOKENS', '1500'))
        self.temperature = float(os.getenv('OPENAI_TEMPERATURE', '0.3'))

    def processar_pergunta(self, pergunta: str, usuario_id: int, empresa_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Processa uma pergunta do usuário e retorna uma resposta inteligente
        """
        start_time = time.time()

        try:
            # 1. Analisar a pergunta e extrair entidades
            entidades = self._extrair_entidades(pergunta)

            # 2. Gerar consulta SQL baseada na pergunta
            sql_query, parametros = self._gerar_consulta_sql(pergunta, entidades, empresa_id)

            # 3. Executar consulta no banco de dados
            dados = self._executar_consulta(sql_query, parametros)

            # 4. Gerar resposta usando IA
            resposta = self._gerar_resposta_ia(pergunta, dados, entidades)

            # 5. Salvar conversa no histórico
            tempo_resposta = int((time.time() - start_time) * 1000)
            self._salvar_conversa(
                usuario_id=usuario_id,
                empresa_id=empresa_id,
                pergunta=pergunta,
                resposta=resposta,
                contexto_sql=sql_query,
                dados_utilizados=dados,
                tempo_resposta=tempo_resposta
            )

            return {
                'success': True,
                'resposta': resposta,
                'dados': dados,
                'sql_utilizada': sql_query if os.getenv('FLASK_ENV') == 'development' else None,
                'tempo_resposta': tempo_resposta,
                'entidades_encontradas': entidades
            }

        except Exception as e:
            return {
                'success': False,
                'erro': str(e),
                'resposta': 'Desculpe, não consegui processar sua pergunta. Pode tentar reformular?'
            }

    def _extrair_entidades(self, pergunta: str) -> Dict[str, Any]:
        """
        Extrai entidades da pergunta (números de nota, nomes de empresa, tipos de tributo, etc.)
        """
        entidades = {
            'numeros_nota': [],
            'empresas': [],
            'tributos': [],
            'produtos': [],
            'clientes': [],
            'anos': [],
            'meses': []
        }

        # Extrair números de nota fiscal
        numeros_nota = re.findall(r'\b\d{3,10}\b', pergunta)
        entidades['numeros_nota'] = numeros_nota

        # Extrair tipos de tributo
        tributos_conhecidos = ['icms', 'ipi', 'pis', 'cofins', 'difal', 'icms-st', 'icms_st']
        for tributo in tributos_conhecidos:
            if tributo.lower() in pergunta.lower():
                entidades['tributos'].append(tributo.lower().replace('-', '_'))

        # Extrair anos
        anos = re.findall(r'\b(20\d{2})\b', pergunta)
        entidades['anos'] = [int(ano) for ano in anos]

        return entidades

    def _gerar_consulta_sql(self, pergunta: str, entidades: Dict, empresa_id: Optional[int]) -> Tuple[str, Dict]:
        """
        Gera consulta SQL baseada na pergunta e entidades extraídas
        """
        # Buscar template mais adequado
        template = self._encontrar_template_adequado(pergunta)

        if template:
            return self._aplicar_template(template, entidades, empresa_id)

        # Se não encontrou template, gerar consulta genérica
        return self._gerar_consulta_generica(pergunta, entidades, empresa_id)

    def _encontrar_template_adequado(self, pergunta: str) -> Optional[Dict]:
        """
        Encontra o template mais adequado para a pergunta
        """
        pergunta_lower = pergunta.lower()

        # Templates específicos baseados em palavras-chave
        templates_internos = {
            'produtos': {
                'sql_template': """
                    SELECT
                        COUNT(DISTINCT p.id) as total_produtos,
                        COUNT(DISTINCT p.empresa_id) as empresas_com_produtos,
                        COUNT(DISTINCT nfi.numero_nf) as notas_com_produtos
                    FROM produto p
                    LEFT JOIN nota_fiscal_item nfi ON p.id = nfi.produto_id
                """,
                'categoria': 'produto'
            },
            'empresas': {
                'sql_template': """
                    SELECT
                        COUNT(DISTINCT e.id) as total_empresas,
                        COUNT(DISTINCT nfi.numero_nf) as total_notas,
                        SUM(nfi.valor_total) as valor_total
                    FROM empresa e
                    LEFT JOIN nota_fiscal_item nfi ON e.id = nfi.empresa_id
                """,
                'categoria': 'empresa'
            },
            'clientes': {
                'sql_template': """
                    SELECT
                        COUNT(DISTINCT c.id) as total_clientes,
                        COUNT(DISTINCT c.empresa_id) as empresas_com_clientes,
                        COUNT(DISTINCT nfi.numero_nf) as notas_com_clientes
                    FROM cliente c
                    LEFT JOIN nota_fiscal_item nfi ON c.id = nfi.cliente_id
                """,
                'categoria': 'cliente'
            }
        }

        # Verificar templates internos primeiro
        for keyword, template in templates_internos.items():
            if keyword in pergunta_lower:
                return template

        try:
            templates = ChatbotTemplates.query.filter_by(ativo=True).all()

            for template in templates:
                # Verificar se a pergunta contém palavras-chave do template
                palavras_template = template.pergunta_template.lower().split()
                palavras_pergunta = pergunta.lower().split()

                matches = sum(1 for palavra in palavras_template if palavra in palavras_pergunta)
                if matches >= len(palavras_template) * 0.6:  # 60% de match
                    return {
                        'categoria': template.categoria,
                        'sql_template': template.sql_template,
                        'pergunta_template': template.pergunta_template
                    }
        except Exception as e:
            print(f"Erro ao buscar templates: {e}")

        return None

    def _aplicar_template(self, template: Dict, entidades: Dict, empresa_id: Optional[int]) -> Tuple[str, Dict]:
        """
        Aplica um template encontrado, substituindo variáveis
        """
        sql = template['sql_template']
        parametros = {}

        # Substituir variáveis do template
        if '{numero}' in sql and entidades['numeros_nota']:
            sql = sql.replace('{numero}', entidades['numeros_nota'][0])

        if '{tipo}' in sql and entidades['tributos']:
            sql = sql.replace('{tipo}', entidades['tributos'][0])

        # Adicionar filtro de empresa se especificado
        if empresa_id and 'empresa_id' not in sql:
            if 'WHERE' in sql.upper():
                sql += f' AND empresa_id = {empresa_id}'
            else:
                sql += f' WHERE empresa_id = {empresa_id}'

        return sql, parametros

    def _gerar_consulta_generica(self, pergunta: str, entidades: Dict, empresa_id: Optional[int]) -> Tuple[str, Dict]:
        """
        Gera uma consulta genérica quando não encontra template específico
        """
        if entidades['numeros_nota']:
            # Buscar por nota fiscal específica
            sql = """
                SELECT nfi.numero_nf, nfi.data_emissao, nfi.valor_total,
                       e.razao_social as empresa_nome, c.razao_social as cliente_nome,
                       p.descricao as produto_nome
                FROM nota_fiscal_item nfi
                LEFT JOIN empresa e ON nfi.empresa_id = e.id
                LEFT JOIN cliente c ON nfi.cliente_id = c.id
                LEFT JOIN produto p ON nfi.produto_id = p.id
                WHERE nfi.numero_nf = %s
            """
            parametros = {'numero_nf': entidades['numeros_nota'][0]}

        elif 'inconsistencia' in pergunta.lower() or 'problema' in pergunta.lower():
            # Buscar inconsistências de auditoria
            sql = """
                SELECT ar.tipo_tributo, ar.status, COUNT(*) as total
                FROM auditoria_resultado ar
                WHERE ar.status = 'inconsistente'
                GROUP BY ar.tipo_tributo, ar.status
                LIMIT 10
            """
            parametros = {}

        elif 'produto' in pergunta.lower():
            # Consulta específica para produtos
            sql = """
                SELECT
                    COUNT(DISTINCT p.id) as total_produtos,
                    COUNT(DISTINCT p.empresa_id) as total_empresas,
                    COUNT(DISTINCT nfi.numero_nf) as notas_com_produtos
                FROM produto p
                LEFT JOIN nota_fiscal_item nfi ON p.id = nfi.produto_id
            """
            parametros = {}

        else:
            # Consulta genérica - estatísticas básicas (sem filtro de data restritivo)
            sql = """
                SELECT
                    COUNT(DISTINCT nfi.numero_nf) as total_notas,
                    COUNT(DISTINCT nfi.empresa_id) as total_empresas,
                    SUM(nfi.valor_total) as valor_total,
                    MIN(nfi.data_emissao) as data_mais_antiga,
                    MAX(nfi.data_emissao) as data_mais_recente
                FROM nota_fiscal_item nfi
            """
            parametros = {}

        # Adicionar filtro de empresa se especificado
        if empresa_id and 'empresa_id' not in sql:
            if 'WHERE' in sql.upper():
                sql += f' AND nfi.empresa_id = {empresa_id}'
            else:
                sql += f' WHERE nfi.empresa_id = {empresa_id}'

        return sql, parametros

    def _executar_consulta(self, sql: str, parametros: Dict) -> List[Dict]:
        """
        Executa a consulta SQL e retorna os resultados
        """
        try:
            result = db.session.execute(text(sql), parametros)
            columns = result.keys()
            rows = result.fetchall()

            # Converter para lista de dicionários
            dados = []
            for row in rows:
                row_dict = {}
                for i, column in enumerate(columns):
                    value = row[i]
                    # Converter tipos especiais para JSON serializável
                    if hasattr(value, 'isoformat'):  # datetime
                        value = value.isoformat()
                    elif hasattr(value, '__float__'):  # Decimal
                        value = float(value)
                    row_dict[column] = value
                dados.append(row_dict)

            return dados

        except Exception as e:
            print(f"Erro ao executar consulta SQL: {e}")
            return []

    def _gerar_resposta_ia(self, pergunta: str, dados: List[Dict], entidades: Dict) -> str:
        """
        Usa a IA para gerar uma resposta natural baseada nos dados
        """
        if not dados:
            return "Não encontrei dados relacionados à sua pergunta. Pode verificar se os parâmetros estão corretos?"

        # Preparar contexto para a IA
        contexto = self._preparar_contexto_ia(dados, entidades)

        prompt = f"""
        Você é um assistente especializado em auditoria fiscal. Responda à pergunta do usuário de forma clara e objetiva, baseando-se apenas nos dados fornecidos.

        Pergunta: {pergunta}

        Dados disponíveis:
        {contexto}

        Instruções:
        - Responda de forma natural e conversacional
        - Use números e valores específicos dos dados
        - Se houver inconsistências, destaque-as
        - Se não houver dados suficientes, seja claro sobre isso
        - Mantenha a resposta focada na pergunta
        - Use formatação clara (quebras de linha, listas quando apropriado)
        """

        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=self.max_tokens,
                temperature=self.temperature
            )

            return response.choices[0].message.content.strip()

        except Exception as e:
            return f"Encontrei os dados, mas houve um erro ao gerar a resposta: {str(e)}"

    def _preparar_contexto_ia(self, dados: List[Dict], entidades: Dict) -> str:
        """
        Prepara o contexto dos dados para enviar à IA
        """
        if not dados:
            return "Nenhum dado encontrado."

        # Limitar quantidade de dados para não exceder limite de tokens
        dados_limitados = dados[:10]  # Máximo 10 registros

        contexto_parts = []
        for i, item in enumerate(dados_limitados, 1):
            item_str = f"Registro {i}:\n"
            for key, value in item.items():
                if value is not None:
                    item_str += f"  {key}: {value}\n"
            contexto_parts.append(item_str)

        if len(dados) > 10:
            contexto_parts.append(f"\n... e mais {len(dados) - 10} registros similares.")

        return "\n".join(contexto_parts)

    def _salvar_conversa(self, usuario_id: int, empresa_id: Optional[int], pergunta: str,
                        resposta: str, contexto_sql: str, dados_utilizados: List[Dict],
                        tempo_resposta: int):
        """
        Salva a conversa no histórico
        """
        try:
            conversa = ChatbotConversas(
                usuario_id=usuario_id,
                empresa_id=empresa_id,
                pergunta=pergunta,
                resposta=resposta,
                contexto_sql=contexto_sql,
                dados_utilizados=dados_utilizados,
                tempo_resposta=tempo_resposta
            )

            db.session.add(conversa)
            db.session.commit()

        except Exception as e:
            print(f"Erro ao salvar conversa: {e}")
            db.session.rollback()

    def obter_historico(self, usuario_id: int, limite: int = 20) -> List[Dict]:
        """
        Obtém o histórico de conversas do usuário
        """
        try:
            conversas = ChatbotConversas.query.filter_by(usuario_id=usuario_id)\
                                             .order_by(ChatbotConversas.data_criacao.desc())\
                                             .limit(limite).all()

            return [conversa.to_dict() for conversa in conversas]
        except Exception as e:
            print(f"Erro ao obter histórico: {e}")
            return []

    def avaliar_resposta(self, conversa_id: int, avaliacao: int, feedback: Optional[str] = None):
        """
        Permite ao usuário avaliar uma resposta do chatbot
        """
        try:
            conversa = ChatbotConversas.query.get(conversa_id)
            if conversa:
                conversa.avaliacao = avaliacao
                conversa.feedback = feedback
                db.session.commit()
                return True
        except Exception as e:
            print(f"Erro ao avaliar resposta: {e}")

        return False
