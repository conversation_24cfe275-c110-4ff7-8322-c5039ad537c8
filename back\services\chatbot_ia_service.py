"""
Serviço de IA para Chatbot do Sistema de Auditoria Fiscal
Integração com OpenAI GPT-4o-mini para responder perguntas sobre auditorias
"""

import os
import json
import re
import time
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import openai
from sqlalchemy import text
from models import db
from models.chatbot_conversas import ChatbotConversas
from models.chatbot_templates import ChatbotTemplates

class ChatbotIAService:
    def __init__(self):
        self.client = openai.OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        self.model = os.getenv('OPENAI_MODEL', 'gpt-4o-mini')
        self.max_tokens = int(os.getenv('OPENAI_MAX_TOKENS', '1500'))
        self.temperature = float(os.getenv('OPENAI_TEMPERATURE', '0.3'))

    def processar_pergunta(self, pergunta: str, usuario_id: int, empresa_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Processa uma pergunta do usuário e retorna uma resposta inteligente
        """
        start_time = time.time()

        try:
            # 1. Analisar a pergunta e extrair entidades
            entidades = self._extrair_entidades(pergunta)

            # 2. Gerar consulta SQL baseada na pergunta
            sql_query, parametros = self._gerar_consulta_sql(pergunta, entidades, empresa_id)

            # 3. Executar consulta no banco de dados
            dados = self._executar_consulta(sql_query, parametros)

            # 4. Gerar resposta usando IA
            resposta = self._gerar_resposta_ia(pergunta, dados, entidades)

            # 5. Salvar conversa no histórico
            tempo_resposta = int((time.time() - start_time) * 1000)
            self._salvar_conversa(
                usuario_id=usuario_id,
                empresa_id=empresa_id,
                pergunta=pergunta,
                resposta=resposta,
                contexto_sql=sql_query,
                dados_utilizados=dados,
                tempo_resposta=tempo_resposta
            )

            return {
                'success': True,
                'resposta': resposta,
                'dados': dados,
                'sql_utilizada': sql_query if os.getenv('FLASK_ENV') == 'development' else None,
                'tempo_resposta': tempo_resposta,
                'entidades_encontradas': entidades
            }

        except Exception as e:
            return {
                'success': False,
                'erro': str(e),
                'resposta': 'Desculpe, não consegui processar sua pergunta. Pode tentar reformular?'
            }

    def _extrair_entidades(self, pergunta: str) -> Dict[str, Any]:
        """
        Extrai entidades da pergunta (números de nota, nomes de empresa, tipos de tributo, etc.)
        """
        entidades = {
            'numeros_nota': [],
            'empresas': [],
            'tributos': [],
            'produtos': [],
            'clientes': [],
            'anos': [],
            'meses': [],
            'valores_monetarios': [],
            'percentuais': [],
            'status': [],
            'tipos_inconsistencia': [],
            'cfops': [],
            'ncms': [],
            'csts': []
        }

        # Extrair números de nota fiscal (3 a 10 dígitos)
        numeros_nota = re.findall(r'\b\d{3,10}\b', pergunta)
        entidades['numeros_nota'] = numeros_nota

        # Extrair tipos de tributo (mais abrangente)
        tributos_conhecidos = [
            'icms', 'ipi', 'pis', 'cofins', 'difal', 'icms-st', 'icms_st',
            'imposto', 'tributo', 'taxa'
        ]
        for tributo in tributos_conhecidos:
            if tributo.lower() in pergunta.lower():
                entidades['tributos'].append(tributo.lower().replace('-', '_'))

        # Extrair anos (2020-2030)
        anos = re.findall(r'\b(20[2-3]\d)\b', pergunta)
        entidades['anos'] = [int(ano) for ano in anos]

        # Extrair meses (nomes e números)
        meses_nomes = {
            'janeiro': 1, 'fevereiro': 2, 'março': 3, 'abril': 4, 'maio': 5, 'junho': 6,
            'julho': 7, 'agosto': 8, 'setembro': 9, 'outubro': 10, 'novembro': 11, 'dezembro': 12,
            'jan': 1, 'fev': 2, 'mar': 3, 'abr': 4, 'mai': 5, 'jun': 6,
            'jul': 7, 'ago': 8, 'set': 9, 'out': 10, 'nov': 11, 'dez': 12
        }

        for nome_mes, numero_mes in meses_nomes.items():
            if nome_mes.lower() in pergunta.lower():
                entidades['meses'].append(numero_mes)

        # Extrair números de mês (1-12)
        meses_numeros = re.findall(r'\b(1[0-2]|[1-9])\b', pergunta)
        for mes in meses_numeros:
            if 1 <= int(mes) <= 12:
                entidades['meses'].append(int(mes))

        # Extrair valores monetários
        valores = re.findall(r'R\$\s*(\d{1,3}(?:\.\d{3})*(?:,\d{2})?)', pergunta)
        entidades['valores_monetarios'] = valores

        # Extrair percentuais
        percentuais = re.findall(r'(\d+(?:,\d+)?)\s*%', pergunta)
        entidades['percentuais'] = percentuais

        # Extrair status
        status_conhecidos = ['conforme', 'inconsistente', 'novo', 'produção', 'producao', 'ativo', 'inativo']
        for status in status_conhecidos:
            if status.lower() in pergunta.lower():
                entidades['status'].append(status.lower().replace('ç', 'c'))

        # Extrair tipos de inconsistência
        inconsistencias = ['valor', 'cst', 'origem', 'aliquota', 'alíquota', 'base de calculo', 'base de cálculo']
        for inconsistencia in inconsistencias:
            if inconsistencia.lower() in pergunta.lower():
                entidades['tipos_inconsistencia'].append(inconsistencia.lower().replace('í', 'i').replace('ç', 'c'))

        # Extrair CFOPs (4 dígitos)
        cfops = re.findall(r'\b([1-7]\d{3})\b', pergunta)
        entidades['cfops'] = cfops

        # Extrair NCMs (8 dígitos)
        ncms = re.findall(r'\b(\d{8})\b', pergunta)
        entidades['ncms'] = ncms

        # Extrair CSTs (2-3 dígitos)
        csts = re.findall(r'\bCST\s*(\d{2,3})\b', pergunta, re.IGNORECASE)
        entidades['csts'] = csts

        return entidades

    def _gerar_consulta_sql(self, pergunta: str, entidades: Dict, empresa_id: Optional[int]) -> Tuple[str, Dict]:
        """
        Gera consulta SQL baseada na pergunta e entidades extraídas
        """
        # Buscar template mais adequado
        template = self._encontrar_template_adequado(pergunta)

        if template:
            return self._aplicar_template(template, entidades, empresa_id)

        # Se não encontrou template, gerar consulta genérica
        return self._gerar_consulta_generica(pergunta, entidades, empresa_id)

    def _encontrar_template_adequado(self, pergunta: str) -> Optional[Dict]:
        """
        Encontra o template mais adequado para a pergunta
        """
        pergunta_lower = pergunta.lower()

        # Templates específicos baseados em palavras-chave e contexto
        templates_internos = {
            # Consultas sobre notas fiscais específicas
            'nota_especifica': {
                'keywords': ['nota', 'nf', 'numero'],
                'sql_template': """
                    SELECT
                        nfi.numero_nf,
                        nfi.chave_nf,
                        nfi.data_emissao,
                        nfi.cfop,
                        nfi.ncm,
                        nfi.valor_total,
                        e.razao_social as empresa_nome,
                        c.razao_social as cliente_nome,
                        p.descricao as produto_nome,
                        p.codigo as produto_codigo,
                        -- Dados dos tributos da nota
                        t.icms_valor, t.icms_aliquota, t.icms_cst, t.icms_vbc,
                        t.ipi_valor, t.ipi_aliquota, t.ipi_cst, t.ipi_vbc,
                        t.pis_valor, t.pis_aliquota, t.pis_cst, t.pis_vbc,
                        t.cofins_valor, t.cofins_aliquota, t.cofins_cst, t.cofins_vbc
                    FROM nota_fiscal_item nfi
                    LEFT JOIN empresa e ON nfi.empresa_id = e.id
                    LEFT JOIN cliente c ON nfi.cliente_id = c.id
                    LEFT JOIN produto p ON nfi.produto_id = p.id
                    LEFT JOIN tributo t ON t.nota_fiscal_item_id = nfi.id
                """,
                'categoria': 'nota_fiscal'
            },

            # Consultas sobre auditorias e inconsistências
            'auditoria_inconsistencias': {
                'keywords': ['inconsistencia', 'inconsistente', 'problema', 'erro', 'auditoria'],
                'sql_template': """
                    SELECT
                        ar.tipo_tributo,
                        ar.status,
                        COUNT(*) as total_registros,
                        SUM(ar.valor_nota) as valor_total_nota,
                        SUM(ar.valor_calculado) as valor_total_calculado,
                        SUM(ar.valor_nota - ar.valor_calculado) as diferenca_total,
                        AVG(ar.valor_nota - ar.valor_calculado) as diferenca_media,
                        -- Detalhes das inconsistências
                        SUM(CASE WHEN ar.inconsistencia_valor THEN 1 ELSE 0 END) as inconsistencias_valor,
                        SUM(CASE WHEN ar.inconsistencia_cst THEN 1 ELSE 0 END) as inconsistencias_cst,
                        SUM(CASE WHEN ar.inconsistencia_aliquota THEN 1 ELSE 0 END) as inconsistencias_aliquota
                    FROM auditoria_resultado ar
                    LEFT JOIN nota_fiscal_item nfi ON ar.nota_fiscal_item_id = nfi.id
                    LEFT JOIN empresa e ON ar.empresa_id = e.id
                """,
                'categoria': 'auditoria'
            },

            # Consultas sobre produtos
            'produtos_detalhados': {
                'keywords': ['produto', 'produtos', 'mercadoria'],
                'sql_template': """
                    SELECT
                        p.codigo,
                        p.descricao,
                        p.cest,
                        COUNT(DISTINCT nfi.numero_nf) as total_notas,
                        COUNT(DISTINCT nfi.cliente_id) as total_clientes,
                        SUM(nfi.valor_total) as valor_total_vendas,
                        AVG(nfi.valor_total) as valor_medio_venda,
                        -- Dados tributários mais comuns
                        COUNT(DISTINCT nfi.cfop) as cfops_utilizados,
                        COUNT(DISTINCT nfi.ncm) as ncms_utilizados
                    FROM produto p
                    LEFT JOIN nota_fiscal_item nfi ON p.id = nfi.produto_id
                    LEFT JOIN empresa e ON p.empresa_id = e.id
                """,
                'categoria': 'produto'
            },

            # Consultas sobre clientes
            'clientes_detalhados': {
                'keywords': ['cliente', 'clientes', 'destinatario'],
                'sql_template': """
                    SELECT
                        c.razao_social,
                        c.cnpj,
                        c.uf,
                        c.municipio,
                        c.atividade,
                        c.destinacao,
                        COUNT(DISTINCT nfi.numero_nf) as total_notas,
                        COUNT(DISTINCT nfi.produto_id) as total_produtos,
                        SUM(nfi.valor_total) as valor_total_compras,
                        AVG(nfi.valor_total) as valor_medio_compra
                    FROM cliente c
                    LEFT JOIN nota_fiscal_item nfi ON c.id = nfi.cliente_id
                    LEFT JOIN empresa e ON c.empresa_id = e.id
                """,
                'categoria': 'cliente'
            },

            # Consultas sobre cenários tributários
            'cenarios_tributarios': {
                'keywords': ['cenario', 'cenários', 'tributario', 'tributária', 'configuracao'],
                'sql_template': """
                    SELECT
                        'icms' as tipo_tributo,
                        ci.status,
                        COUNT(*) as total_cenarios,
                        AVG(ci.aliquota) as aliquota_media
                    FROM cenario_icms ci
                    LEFT JOIN empresa e ON ci.empresa_id = e.id
                    UNION ALL
                    SELECT
                        'ipi' as tipo_tributo,
                        cip.status,
                        COUNT(*) as total_cenarios,
                        AVG(cip.aliquota) as aliquota_media
                    FROM cenario_ipi cip
                    LEFT JOIN empresa e ON cip.empresa_id = e.id
                """,
                'categoria': 'cenario'
            },

            # Consultas estatísticas gerais
            'estatisticas_gerais': {
                'keywords': ['total', 'quantidade', 'quantos', 'estatistica', 'resumo'],
                'sql_template': """
                    SELECT
                        COUNT(DISTINCT nfi.numero_nf) as total_notas,
                        COUNT(DISTINCT nfi.empresa_id) as total_empresas,
                        COUNT(DISTINCT nfi.cliente_id) as total_clientes,
                        COUNT(DISTINCT nfi.produto_id) as total_produtos,
                        SUM(nfi.valor_total) as valor_total_operacoes,
                        AVG(nfi.valor_total) as valor_medio_operacao,
                        MIN(nfi.data_emissao) as data_mais_antiga,
                        MAX(nfi.data_emissao) as data_mais_recente,
                        -- Estatísticas de auditoria
                        (SELECT COUNT(*) FROM auditoria_resultado WHERE status = 'conforme') as total_conforme,
                        (SELECT COUNT(*) FROM auditoria_resultado WHERE status = 'inconsistente') as total_inconsistente
                    FROM nota_fiscal_item nfi
                """,
                'categoria': 'estatistica'
            }
        }

        # Verificar templates internos primeiro
        for template_name, template_data in templates_internos.items():
            keywords = template_data['keywords']
            if any(keyword in pergunta_lower for keyword in keywords):
                return {
                    'categoria': template_data['categoria'],
                    'sql_template': template_data['sql_template'],
                    'pergunta_template': template_name
                }

        # Buscar templates do banco de dados
        try:
            templates = ChatbotTemplates.query.filter_by(ativo=True).all()

            for template in templates:
                # Verificar se a pergunta contém palavras-chave do template
                palavras_template = template.pergunta_template.lower().split()
                palavras_pergunta = pergunta.lower().split()

                matches = sum(1 for palavra in palavras_template if palavra in palavras_pergunta)
                if matches >= len(palavras_template) * 0.6:  # 60% de match
                    return {
                        'categoria': template.categoria,
                        'sql_template': template.sql_template,
                        'pergunta_template': template.pergunta_template
                    }
        except Exception as e:
            print(f"Erro ao buscar templates: {e}")

        return None

    def _aplicar_template(self, template: Dict, entidades: Dict, empresa_id: Optional[int]) -> Tuple[str, Dict]:
        """
        Aplica um template encontrado, substituindo variáveis e adicionando filtros
        """
        sql = template['sql_template']
        parametros = {}
        where_conditions = []

        # Adicionar filtros baseados nas entidades extraídas

        # Filtro por empresa
        if empresa_id:
            where_conditions.append(f"e.id = {empresa_id}")

        # Filtro por número de nota fiscal
        if entidades['numeros_nota']:
            numero_nota = entidades['numeros_nota'][0]
            where_conditions.append(f"nfi.numero_nf = '{numero_nota}'")

        # Filtro por tipo de tributo (para consultas de auditoria)
        if entidades['tributos'] and 'auditoria_resultado' in sql:
            tributo = entidades['tributos'][0]
            where_conditions.append(f"ar.tipo_tributo = '{tributo}'")

        # Filtro por status
        if entidades['status']:
            status = entidades['status'][0]
            if 'auditoria_resultado' in sql:
                where_conditions.append(f"ar.status = '{status}'")
            elif 'cenario_' in sql:
                where_conditions.append(f"status = '{status}'")

        # Filtro por ano
        if entidades['anos']:
            ano = entidades['anos'][0]
            where_conditions.append(f"EXTRACT(YEAR FROM nfi.data_emissao) = {ano}")

        # Filtro por mês
        if entidades['meses']:
            mes = entidades['meses'][0]
            where_conditions.append(f"EXTRACT(MONTH FROM nfi.data_emissao) = {mes}")

        # Filtro por CFOP
        if entidades['cfops']:
            cfop = entidades['cfops'][0]
            where_conditions.append(f"nfi.cfop = '{cfop}'")

        # Filtro por NCM
        if entidades['ncms']:
            ncm = entidades['ncms'][0]
            where_conditions.append(f"nfi.ncm = '{ncm}'")

        # Filtro por CST
        if entidades['csts']:
            cst = entidades['csts'][0]
            if 'icms' in template.get('categoria', ''):
                where_conditions.append(f"t.icms_cst = '{cst}'")
            elif 'ipi' in template.get('categoria', ''):
                where_conditions.append(f"t.ipi_cst = '{cst}'")

        # Aplicar filtros WHERE
        if where_conditions:
            if 'WHERE' in sql.upper():
                sql += ' AND ' + ' AND '.join(where_conditions)
            else:
                sql += ' WHERE ' + ' AND '.join(where_conditions)

        # Adicionar GROUP BY se necessário para consultas agregadas
        if 'COUNT(' in sql or 'SUM(' in sql or 'AVG(' in sql:
            if 'GROUP BY' not in sql.upper():
                # Determinar campos para GROUP BY baseado no tipo de consulta
                if template.get('categoria') == 'auditoria':
                    sql += ' GROUP BY ar.tipo_tributo, ar.status'
                elif template.get('categoria') == 'produto':
                    sql += ' GROUP BY p.id, p.codigo, p.descricao, p.cest'
                elif template.get('categoria') == 'cliente':
                    sql += ' GROUP BY c.id, c.razao_social, c.cnpj, c.uf, c.municipio, c.atividade, c.destinacao'

        # Adicionar ORDER BY para melhor apresentação
        if 'ORDER BY' not in sql.upper():
            if template.get('categoria') == 'nota_fiscal':
                sql += ' ORDER BY nfi.data_emissao DESC'
            elif template.get('categoria') == 'auditoria':
                sql += ' ORDER BY total_registros DESC'
            elif template.get('categoria') in ['produto', 'cliente']:
                sql += ' ORDER BY total_notas DESC'

        # Limitar resultados para evitar sobrecarga
        if 'LIMIT' not in sql.upper():
            sql += ' LIMIT 50'

        return sql, parametros

    def _gerar_consulta_generica(self, pergunta: str, entidades: Dict, empresa_id: Optional[int]) -> Tuple[str, Dict]:
        """
        Gera uma consulta genérica quando não encontra template específico
        """
        parametros = {}
        where_conditions = []

        # Adicionar filtro de empresa sempre que possível
        if empresa_id:
            where_conditions.append(f"nfi.empresa_id = {empresa_id}")

        # Priorizar consultas baseadas nas entidades encontradas
        if entidades['numeros_nota']:
            # Consulta detalhada para nota fiscal específica
            numero_nota = entidades['numeros_nota'][0]
            sql = """
                SELECT
                    nfi.numero_nf,
                    nfi.chave_nf,
                    nfi.data_emissao,
                    nfi.cfop,
                    nfi.ncm,
                    nfi.valor_total,
                    e.razao_social as empresa_nome,
                    c.razao_social as cliente_nome,
                    c.cnpj as cliente_cnpj,
                    c.uf as cliente_uf,
                    p.descricao as produto_nome,
                    p.codigo as produto_codigo,
                    -- Dados tributários da nota
                    t.icms_valor, t.icms_aliquota, t.icms_cst, t.icms_vbc,
                    t.ipi_valor, t.ipi_aliquota, t.ipi_cst, t.ipi_vbc,
                    t.pis_valor, t.pis_aliquota, t.pis_cst, t.pis_vbc,
                    t.cofins_valor, t.cofins_aliquota, t.cofins_cst, t.cofins_vbc,
                    -- Dados de auditoria se existirem
                    ar.status as auditoria_status,
                    ar.valor_calculado as valor_auditoria_calculado,
                    ar.tipo_tributo as tributo_auditado
                FROM nota_fiscal_item nfi
                LEFT JOIN empresa e ON nfi.empresa_id = e.id
                LEFT JOIN cliente c ON nfi.cliente_id = c.id
                LEFT JOIN produto p ON nfi.produto_id = p.id
                LEFT JOIN tributo t ON t.nota_fiscal_item_id = nfi.id
                LEFT JOIN auditoria_resultado ar ON ar.nota_fiscal_item_id = nfi.id
            """
            where_conditions.append(f"nfi.numero_nf = '{numero_nota}'")

        elif any(keyword in pergunta.lower() for keyword in ['inconsistencia', 'inconsistente', 'problema', 'erro', 'auditoria']):
            # Consulta para inconsistências de auditoria
            sql = """
                SELECT
                    ar.tipo_tributo,
                    ar.status,
                    COUNT(*) as total_registros,
                    SUM(ar.valor_nota) as valor_total_nota,
                    SUM(ar.valor_calculado) as valor_total_calculado,
                    SUM(ar.valor_nota - ar.valor_calculado) as diferenca_total,
                    AVG(ABS(ar.valor_nota - ar.valor_calculado)) as diferenca_media,
                    -- Tipos de inconsistências
                    SUM(CASE WHEN ar.inconsistencia_valor THEN 1 ELSE 0 END) as inconsistencias_valor,
                    SUM(CASE WHEN ar.inconsistencia_cst THEN 1 ELSE 0 END) as inconsistencias_cst,
                    SUM(CASE WHEN ar.inconsistencia_aliquota THEN 1 ELSE 0 END) as inconsistencias_aliquota,
                    SUM(CASE WHEN ar.inconsistencia_base_calculo THEN 1 ELSE 0 END) as inconsistencias_base_calculo
                FROM auditoria_resultado ar
                LEFT JOIN nota_fiscal_item nfi ON ar.nota_fiscal_item_id = nfi.id
                LEFT JOIN empresa e ON ar.empresa_id = e.id
            """
            if not entidades['status'] or 'inconsistente' in entidades['status']:
                where_conditions.append("ar.status = 'inconsistente'")

        elif any(keyword in pergunta.lower() for keyword in ['produto', 'produtos', 'mercadoria']):
            # Consulta detalhada para produtos
            sql = """
                SELECT
                    p.codigo,
                    p.descricao,
                    p.cest,
                    e.razao_social as empresa_nome,
                    COUNT(DISTINCT nfi.numero_nf) as total_notas,
                    COUNT(DISTINCT nfi.cliente_id) as total_clientes,
                    SUM(nfi.valor_total) as valor_total_vendas,
                    AVG(nfi.valor_total) as valor_medio_venda,
                    MIN(nfi.data_emissao) as primeira_venda,
                    MAX(nfi.data_emissao) as ultima_venda,
                    -- Dados tributários
                    COUNT(DISTINCT nfi.cfop) as cfops_utilizados,
                    COUNT(DISTINCT nfi.ncm) as ncms_utilizados
                FROM produto p
                LEFT JOIN nota_fiscal_item nfi ON p.id = nfi.produto_id
                LEFT JOIN empresa e ON p.empresa_id = e.id
            """

        elif any(keyword in pergunta.lower() for keyword in ['cliente', 'clientes', 'destinatario']):
            # Consulta detalhada para clientes
            sql = """
                SELECT
                    c.razao_social,
                    c.cnpj,
                    c.uf,
                    c.municipio,
                    c.atividade,
                    c.destinacao,
                    e.razao_social as empresa_nome,
                    COUNT(DISTINCT nfi.numero_nf) as total_notas,
                    COUNT(DISTINCT nfi.produto_id) as total_produtos,
                    SUM(nfi.valor_total) as valor_total_compras,
                    AVG(nfi.valor_total) as valor_medio_compra,
                    MIN(nfi.data_emissao) as primeira_compra,
                    MAX(nfi.data_emissao) as ultima_compra
                FROM cliente c
                LEFT JOIN nota_fiscal_item nfi ON c.id = nfi.cliente_id
                LEFT JOIN empresa e ON c.empresa_id = e.id
            """

        else:
            # Consulta estatística geral
            sql = """
                SELECT
                    COUNT(DISTINCT nfi.numero_nf) as total_notas,
                    COUNT(DISTINCT nfi.empresa_id) as total_empresas,
                    COUNT(DISTINCT nfi.cliente_id) as total_clientes,
                    COUNT(DISTINCT nfi.produto_id) as total_produtos,
                    SUM(nfi.valor_total) as valor_total_operacoes,
                    AVG(nfi.valor_total) as valor_medio_operacao,
                    MIN(nfi.data_emissao) as data_mais_antiga,
                    MAX(nfi.data_emissao) as data_mais_recente,
                    -- Estatísticas de auditoria
                    (SELECT COUNT(*) FROM auditoria_resultado ar2 WHERE ar2.status = 'conforme'
                     {empresa_filter}) as total_conforme,
                    (SELECT COUNT(*) FROM auditoria_resultado ar2 WHERE ar2.status = 'inconsistente'
                     {empresa_filter}) as total_inconsistente
                FROM nota_fiscal_item nfi
            """.format(
                empresa_filter=f"AND ar2.empresa_id = {empresa_id}" if empresa_id else ""
            )

        # Aplicar filtros adicionais baseados nas entidades
        if entidades['tributos'] and 'auditoria_resultado' in sql:
            tributo = entidades['tributos'][0]
            where_conditions.append(f"ar.tipo_tributo = '{tributo}'")

        if entidades['anos']:
            ano = entidades['anos'][0]
            where_conditions.append(f"EXTRACT(YEAR FROM nfi.data_emissao) = {ano}")

        if entidades['meses']:
            mes = entidades['meses'][0]
            where_conditions.append(f"EXTRACT(MONTH FROM nfi.data_emissao) = {mes}")

        if entidades['cfops']:
            cfop = entidades['cfops'][0]
            where_conditions.append(f"nfi.cfop = '{cfop}'")

        if entidades['ncms']:
            ncm = entidades['ncms'][0]
            where_conditions.append(f"nfi.ncm = '{ncm}'")

        # Aplicar condições WHERE
        if where_conditions:
            sql += ' WHERE ' + ' AND '.join(where_conditions)

        # Adicionar GROUP BY para consultas agregadas
        if 'COUNT(' in sql or 'SUM(' in sql or 'AVG(' in sql:
            if 'GROUP BY' not in sql.upper() and not entidades['numeros_nota']:
                if 'produto' in pergunta.lower():
                    sql += ' GROUP BY p.id, p.codigo, p.descricao, p.cest, e.razao_social'
                elif 'cliente' in pergunta.lower():
                    sql += ' GROUP BY c.id, c.razao_social, c.cnpj, c.uf, c.municipio, c.atividade, c.destinacao, e.razao_social'
                elif 'auditoria' in sql:
                    sql += ' GROUP BY ar.tipo_tributo, ar.status'

        # Adicionar ORDER BY
        if 'ORDER BY' not in sql.upper():
            if entidades['numeros_nota']:
                sql += ' ORDER BY nfi.data_emissao DESC'
            elif 'auditoria' in sql:
                sql += ' ORDER BY total_registros DESC'
            elif any(keyword in pergunta.lower() for keyword in ['produto', 'cliente']):
                sql += ' ORDER BY total_notas DESC'
            else:
                sql += ' ORDER BY nfi.data_emissao DESC'

        # Limitar resultados
        if 'LIMIT' not in sql.upper():
            sql += ' LIMIT 50'

        return sql, parametros

    def _executar_consulta(self, sql: str, parametros: Dict) -> List[Dict]:
        """
        Executa a consulta SQL e retorna os resultados
        """
        try:
            result = db.session.execute(text(sql), parametros)
            columns = result.keys()
            rows = result.fetchall()

            # Converter para lista de dicionários
            dados = []
            for row in rows:
                row_dict = {}
                for i, column in enumerate(columns):
                    value = row[i]
                    # Converter tipos especiais para JSON serializável
                    if hasattr(value, 'isoformat'):  # datetime
                        value = value.isoformat()
                    elif hasattr(value, '__float__'):  # Decimal
                        value = float(value)
                    row_dict[column] = value
                dados.append(row_dict)

            return dados

        except Exception as e:
            print(f"Erro ao executar consulta SQL: {e}")
            return []

    def _gerar_resposta_ia(self, pergunta: str, dados: List[Dict], entidades: Dict) -> str:
        """
        Usa a IA para gerar uma resposta natural baseada nos dados
        """
        if not dados:
            return "Não encontrei dados relacionados à sua pergunta. Pode verificar se os parâmetros estão corretos ou se há dados disponíveis para o período/empresa especificados?"

        # Preparar contexto para a IA
        contexto = self._preparar_contexto_ia(dados, entidades)

        # Determinar o tipo de resposta baseado na pergunta e dados
        tipo_resposta = self._determinar_tipo_resposta(pergunta, dados, entidades)

        prompt = f"""
        Você é um assistente especializado em auditoria fiscal brasileira. Você tem conhecimento profundo sobre:
        - Tributos: ICMS, ICMS-ST, IPI, PIS, COFINS, DIFAL
        - Códigos fiscais: CST, CFOP, NCM
        - Processos de auditoria e conformidade fiscal
        - Análise de inconsistências tributárias

        Pergunta do usuário: {pergunta}

        Dados encontrados:
        {contexto}

        Tipo de análise: {tipo_resposta}

        Instruções específicas:
        1. Responda de forma técnica mas acessível
        2. Use os valores exatos dos dados fornecidos
        3. Para inconsistências, explique o que pode estar causando o problema
        4. Para consultas sobre notas fiscais, forneça um resumo completo
        5. Para estatísticas, destaque os pontos mais relevantes
        6. Se houver dados de auditoria, compare valores da nota vs. calculados
        7. Use formatação clara com quebras de linha e listas
        8. Mencione datas, valores monetários e percentuais quando relevantes
        9. Se identificar problemas fiscais, sugira possíveis causas
        10. Mantenha o foco na pergunta específica do usuário

        Contexto adicional sobre os dados:
        - Valores em "valor_nota" são os tributos declarados na nota fiscal
        - Valores em "valor_calculado" são os tributos calculados pelo sistema baseado nos cenários
        - Status "conforme" indica que os valores batem
        - Status "inconsistente" indica divergências que precisam ser analisadas
        - CST define o regime tributário aplicável
        - CFOP indica a natureza da operação
        - NCM classifica a mercadoria para fins tributários
        """

        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=self.max_tokens,
                temperature=self.temperature
            )

            return response.choices[0].message.content.strip()

        except Exception as e:
            return f"Encontrei os dados, mas houve um erro ao gerar a resposta: {str(e)}"

    def _determinar_tipo_resposta(self, pergunta: str, dados: List[Dict], entidades: Dict) -> str:
        """
        Determina o tipo de resposta baseado na pergunta e dados
        """
        pergunta_lower = pergunta.lower()

        if entidades['numeros_nota']:
            return "Análise detalhada de nota fiscal específica"
        elif any(keyword in pergunta_lower for keyword in ['inconsistencia', 'inconsistente', 'problema', 'erro']):
            return "Análise de inconsistências fiscais"
        elif any(keyword in pergunta_lower for keyword in ['produto', 'produtos']):
            return "Análise de produtos e operações"
        elif any(keyword in pergunta_lower for keyword in ['cliente', 'clientes']):
            return "Análise de clientes e destinatários"
        elif any(keyword in pergunta_lower for keyword in ['auditoria', 'conformidade']):
            return "Relatório de auditoria fiscal"
        elif any(keyword in pergunta_lower for keyword in ['total', 'quantidade', 'estatistica']):
            return "Relatório estatístico geral"
        else:
            return "Consulta geral sobre dados fiscais"

    def _preparar_contexto_ia(self, dados: List[Dict], entidades: Dict) -> str:
        """
        Prepara o contexto dos dados para enviar à IA de forma estruturada e inteligente
        """
        if not dados:
            return "Nenhum dado encontrado."

        # Limitar quantidade de dados para não exceder limite de tokens
        dados_limitados = dados[:15]  # Máximo 15 registros

        contexto_parts = []

        # Detectar tipo de dados e formatar adequadamente
        primeiro_registro = dados_limitados[0]

        # Formatação para nota fiscal específica
        if 'numero_nf' in primeiro_registro and len(dados_limitados) == 1:
            nota = primeiro_registro
            contexto_parts.append("=== DETALHES DA NOTA FISCAL ===")
            contexto_parts.append(f"Número: {nota.get('numero_nf', 'N/A')}")
            contexto_parts.append(f"Data de Emissão: {nota.get('data_emissao', 'N/A')}")
            contexto_parts.append(f"CFOP: {nota.get('cfop', 'N/A')}")
            contexto_parts.append(f"NCM: {nota.get('ncm', 'N/A')}")
            contexto_parts.append(f"Valor Total: R$ {nota.get('valor_total', 0):,.2f}")

            contexto_parts.append("\n--- DADOS DA EMPRESA ---")
            contexto_parts.append(f"Empresa: {nota.get('empresa_nome', 'N/A')}")

            contexto_parts.append("\n--- DADOS DO CLIENTE ---")
            contexto_parts.append(f"Cliente: {nota.get('cliente_nome', 'N/A')}")
            contexto_parts.append(f"CNPJ: {nota.get('cliente_cnpj', 'N/A')}")
            contexto_parts.append(f"UF: {nota.get('cliente_uf', 'N/A')}")

            contexto_parts.append("\n--- DADOS DO PRODUTO ---")
            contexto_parts.append(f"Produto: {nota.get('produto_nome', 'N/A')}")
            contexto_parts.append(f"Código: {nota.get('produto_codigo', 'N/A')}")

            # Dados tributários
            contexto_parts.append("\n--- TRIBUTOS DA NOTA ---")
            if nota.get('icms_valor'):
                contexto_parts.append(f"ICMS: R$ {nota.get('icms_valor', 0):,.2f} (Alíquota: {nota.get('icms_aliquota', 0)}%, CST: {nota.get('icms_cst', 'N/A')})")
            if nota.get('ipi_valor'):
                contexto_parts.append(f"IPI: R$ {nota.get('ipi_valor', 0):,.2f} (Alíquota: {nota.get('ipi_aliquota', 0)}%, CST: {nota.get('ipi_cst', 'N/A')})")
            if nota.get('pis_valor'):
                contexto_parts.append(f"PIS: R$ {nota.get('pis_valor', 0):,.2f} (Alíquota: {nota.get('pis_aliquota', 0)}%, CST: {nota.get('pis_cst', 'N/A')})")
            if nota.get('cofins_valor'):
                contexto_parts.append(f"COFINS: R$ {nota.get('cofins_valor', 0):,.2f} (Alíquota: {nota.get('cofins_aliquota', 0)}%, CST: {nota.get('cofins_cst', 'N/A')})")

            # Dados de auditoria se existirem
            if nota.get('auditoria_status'):
                contexto_parts.append("\n--- RESULTADO DA AUDITORIA ---")
                contexto_parts.append(f"Status: {nota.get('auditoria_status', 'N/A')}")
                contexto_parts.append(f"Tributo Auditado: {nota.get('tributo_auditado', 'N/A')}")
                if nota.get('valor_auditoria_calculado'):
                    contexto_parts.append(f"Valor Calculado: R$ {nota.get('valor_auditoria_calculado', 0):,.2f}")

        # Formatação para dados de auditoria/inconsistências
        elif 'tipo_tributo' in primeiro_registro and 'status' in primeiro_registro:
            contexto_parts.append("=== RESULTADOS DE AUDITORIA ===")
            for i, item in enumerate(dados_limitados, 1):
                contexto_parts.append(f"\n{i}. Tributo: {item.get('tipo_tributo', 'N/A').upper()}")
                contexto_parts.append(f"   Status: {item.get('status', 'N/A')}")
                contexto_parts.append(f"   Total de Registros: {item.get('total_registros', 0):,}")

                if item.get('valor_total_nota') is not None:
                    contexto_parts.append(f"   Valor Total na Nota: R$ {item.get('valor_total_nota', 0):,.2f}")
                if item.get('valor_total_calculado') is not None:
                    contexto_parts.append(f"   Valor Total Calculado: R$ {item.get('valor_total_calculado', 0):,.2f}")
                if item.get('diferenca_total') is not None:
                    diferenca = item.get('diferenca_total', 0)
                    contexto_parts.append(f"   Diferença Total: R$ {diferenca:,.2f}")

                # Tipos de inconsistências
                inconsistencias = []
                if item.get('inconsistencias_valor', 0) > 0:
                    inconsistencias.append(f"Valor ({item.get('inconsistencias_valor', 0)})")
                if item.get('inconsistencias_cst', 0) > 0:
                    inconsistencias.append(f"CST ({item.get('inconsistencias_cst', 0)})")
                if item.get('inconsistencias_aliquota', 0) > 0:
                    inconsistencias.append(f"Alíquota ({item.get('inconsistencias_aliquota', 0)})")
                if item.get('inconsistencias_base_calculo', 0) > 0:
                    inconsistencias.append(f"Base de Cálculo ({item.get('inconsistencias_base_calculo', 0)})")

                if inconsistencias:
                    contexto_parts.append(f"   Tipos de Inconsistências: {', '.join(inconsistencias)}")

        # Formatação para dados de produtos
        elif 'codigo' in primeiro_registro and 'descricao' in primeiro_registro:
            contexto_parts.append("=== DADOS DE PRODUTOS ===")
            for i, item in enumerate(dados_limitados, 1):
                contexto_parts.append(f"\n{i}. Produto: {item.get('descricao', 'N/A')}")
                contexto_parts.append(f"   Código: {item.get('codigo', 'N/A')}")
                if item.get('cest'):
                    contexto_parts.append(f"   CEST: {item.get('cest')}")
                if item.get('total_notas'):
                    contexto_parts.append(f"   Total de Notas: {item.get('total_notas', 0):,}")
                if item.get('valor_total_vendas'):
                    contexto_parts.append(f"   Valor Total de Vendas: R$ {item.get('valor_total_vendas', 0):,.2f}")
                if item.get('valor_medio_venda'):
                    contexto_parts.append(f"   Valor Médio por Venda: R$ {item.get('valor_medio_venda', 0):,.2f}")

        # Formatação para dados de clientes
        elif 'razao_social' in primeiro_registro and 'cnpj' in primeiro_registro:
            contexto_parts.append("=== DADOS DE CLIENTES ===")
            for i, item in enumerate(dados_limitados, 1):
                contexto_parts.append(f"\n{i}. Cliente: {item.get('razao_social', 'N/A')}")
                contexto_parts.append(f"   CNPJ: {item.get('cnpj', 'N/A')}")
                contexto_parts.append(f"   UF: {item.get('uf', 'N/A')}")
                if item.get('municipio'):
                    contexto_parts.append(f"   Município: {item.get('municipio')}")
                if item.get('atividade'):
                    contexto_parts.append(f"   Atividade: {item.get('atividade')}")
                if item.get('total_notas'):
                    contexto_parts.append(f"   Total de Notas: {item.get('total_notas', 0):,}")
                if item.get('valor_total_compras'):
                    contexto_parts.append(f"   Valor Total de Compras: R$ {item.get('valor_total_compras', 0):,.2f}")

        # Formatação para estatísticas gerais
        elif 'total_notas' in primeiro_registro and len(dados_limitados) == 1:
            stats = primeiro_registro
            contexto_parts.append("=== ESTATÍSTICAS GERAIS ===")
            contexto_parts.append(f"Total de Notas Fiscais: {stats.get('total_notas', 0):,}")
            contexto_parts.append(f"Total de Empresas: {stats.get('total_empresas', 0):,}")
            contexto_parts.append(f"Total de Clientes: {stats.get('total_clientes', 0):,}")
            contexto_parts.append(f"Total de Produtos: {stats.get('total_produtos', 0):,}")

            if stats.get('valor_total_operacoes'):
                contexto_parts.append(f"Valor Total das Operações: R$ {stats.get('valor_total_operacoes', 0):,.2f}")
            if stats.get('valor_medio_operacao'):
                contexto_parts.append(f"Valor Médio por Operação: R$ {stats.get('valor_medio_operacao', 0):,.2f}")

            contexto_parts.append(f"Período: {stats.get('data_mais_antiga', 'N/A')} a {stats.get('data_mais_recente', 'N/A')}")

            # Estatísticas de auditoria
            if stats.get('total_conforme') is not None:
                contexto_parts.append(f"\n--- AUDITORIA ---")
                contexto_parts.append(f"Registros Conformes: {stats.get('total_conforme', 0):,}")
                contexto_parts.append(f"Registros Inconsistentes: {stats.get('total_inconsistente', 0):,}")

                total_auditoria = stats.get('total_conforme', 0) + stats.get('total_inconsistente', 0)
                if total_auditoria > 0:
                    percentual_conforme = (stats.get('total_conforme', 0) / total_auditoria) * 100
                    contexto_parts.append(f"Percentual de Conformidade: {percentual_conforme:.1f}%")

        # Formatação genérica para outros tipos de dados
        else:
            contexto_parts.append("=== DADOS ENCONTRADOS ===")
            for i, item in enumerate(dados_limitados, 1):
                contexto_parts.append(f"\nRegistro {i}:")
                for key, value in item.items():
                    if value is not None:
                        # Formatação especial para valores monetários
                        if 'valor' in key.lower() and isinstance(value, (int, float)):
                            contexto_parts.append(f"  {key}: R$ {value:,.2f}")
                        # Formatação especial para datas
                        elif 'data' in key.lower():
                            contexto_parts.append(f"  {key}: {value}")
                        else:
                            contexto_parts.append(f"  {key}: {value}")

        # Adicionar informação sobre registros omitidos
        if len(dados) > 15:
            contexto_parts.append(f"\n... e mais {len(dados) - 15} registros similares não exibidos.")

        # Adicionar resumo estatístico se houver muitos registros
        if len(dados) > 5:
            contexto_parts.append(f"\nTotal de registros encontrados: {len(dados)}")

        return "\n".join(contexto_parts)

    def _salvar_conversa(self, usuario_id: int, empresa_id: Optional[int], pergunta: str,
                        resposta: str, contexto_sql: str, dados_utilizados: List[Dict],
                        tempo_resposta: int):
        """
        Salva a conversa no histórico
        """
        try:
            conversa = ChatbotConversas(
                usuario_id=usuario_id,
                empresa_id=empresa_id,
                pergunta=pergunta,
                resposta=resposta,
                contexto_sql=contexto_sql,
                dados_utilizados=dados_utilizados,
                tempo_resposta=tempo_resposta
            )

            db.session.add(conversa)
            db.session.commit()

        except Exception as e:
            print(f"Erro ao salvar conversa: {e}")
            db.session.rollback()

    def obter_historico(self, usuario_id: int, limite: int = 20) -> List[Dict]:
        """
        Obtém o histórico de conversas do usuário
        """
        try:
            conversas = ChatbotConversas.query.filter_by(usuario_id=usuario_id)\
                                             .order_by(ChatbotConversas.data_criacao.desc())\
                                             .limit(limite).all()

            return [conversa.to_dict() for conversa in conversas]
        except Exception as e:
            print(f"Erro ao obter histórico: {e}")
            return []

    def avaliar_resposta(self, conversa_id: int, avaliacao: int, feedback: Optional[str] = None):
        """
        Permite ao usuário avaliar uma resposta do chatbot
        """
        try:
            conversa = ChatbotConversas.query.get(conversa_id)
            if conversa:
                conversa.avaliacao = avaliacao
                conversa.feedback = feedback
                db.session.commit()
                return True
        except Exception as e:
            print(f"Erro ao avaliar resposta: {e}")

        return False
