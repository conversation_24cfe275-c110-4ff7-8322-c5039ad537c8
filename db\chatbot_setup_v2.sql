-- =====================================================
-- SETUP DO CHATBOT IA - VERSÃO 2.0
-- Sistema de Auditoria Fiscal
-- =====================================================

-- Definir encoding correto
SET client_encoding = 'UTF8';

-- Tabela para armazenar conversas do chatbot
CREATE TABLE IF NOT EXISTS chatbot_conversas (
    id SERIAL PRIMARY KEY,
    usuario_id INTEGER NOT NULL REFERENCES usuario(id) ON DELETE CASCADE,
    empresa_id INTEGER REFERENCES empresa(id) ON DELETE SET NULL,
    escritorio_id INTEGER REFERENCES escritorio(id) ON DELETE SET NULL,
    pergunta TEXT NOT NULL,
    resposta TEXT NOT NULL,
    contexto_sql TEXT,
    dados_utilizados JSONB,
    tempo_resposta INTEGER, -- em milissegundos
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    avaliacao INTEGER CHECK (avaliacao >= 1 AND avaliacao <= 5),
    feedback TEXT
);

-- Tabela para templates de perguntas frequentes
CREATE TABLE IF NOT EXISTS chatbot_templates (
    id SERIAL PRIMARY KEY,
    categoria VARCHAR(50) NOT NULL,
    pergunta_template TEXT NOT NULL,
    sql_template TEXT NOT NULL,
    descricao TEXT,
    ativo BOOLEAN DEFAULT TRUE,
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_chatbot_conversas_usuario ON chatbot_conversas(usuario_id);
CREATE INDEX IF NOT EXISTS idx_chatbot_conversas_empresa ON chatbot_conversas(empresa_id);
CREATE INDEX IF NOT EXISTS idx_chatbot_conversas_data ON chatbot_conversas(data_criacao);
CREATE INDEX IF NOT EXISTS idx_chatbot_templates_categoria ON chatbot_templates(categoria);
CREATE INDEX IF NOT EXISTS idx_chatbot_templates_ativo ON chatbot_templates(ativo);

-- Inserir templates básicos de perguntas
INSERT INTO chatbot_templates (categoria, pergunta_template, sql_template, descricao) VALUES
('nota_fiscal', 'nota {numero}', 
 'SELECT nfi.numero_nf, nfi.data_emissao, nfi.valor_total, e.razao_social as empresa_nome, c.razao_social as cliente_nome, p.descricao as produto_nome FROM nota_fiscal_item nfi LEFT JOIN empresa e ON nfi.empresa_id = e.id LEFT JOIN cliente c ON nfi.cliente_id = c.id LEFT JOIN produto p ON nfi.produto_id = p.id WHERE nfi.numero_nf = ''{numero}''',
 'Busca informações de uma nota fiscal específica'),

('empresa', 'empresa {nome}',
 'SELECT e.razao_social, e.cnpj, e.inscricao_estadual, COUNT(nfi.id) as total_notas FROM empresa e LEFT JOIN nota_fiscal_item nfi ON e.id = nfi.empresa_id WHERE e.razao_social ILIKE ''%{nome}%'' GROUP BY e.id',
 'Busca informações de uma empresa'),

('auditoria', 'inconsistencias',
 'SELECT ar.tipo_tributo, ar.status, COUNT(*) as total FROM auditoria_resultado ar WHERE ar.status = ''inconsistente'' GROUP BY ar.tipo_tributo, ar.status',
 'Busca inconsistências de auditoria'),

('tributo', 'tributo {tipo}',
 'SELECT t.*, nfi.numero_nf FROM tributo t LEFT JOIN nota_fiscal_item nfi ON t.nota_fiscal_item_id = nfi.id WHERE ''{tipo}'' IN (''icms'', ''ipi'', ''pis'', ''cofins'') LIMIT 10',
 'Busca informações de tributos'),

('cliente', 'cliente {nome}',
 'SELECT c.razao_social, c.cnpj, c.atividade, COUNT(nfi.id) as total_notas FROM cliente c LEFT JOIN nota_fiscal_item nfi ON c.id = nfi.cliente_id WHERE c.razao_social ILIKE ''%{nome}%'' GROUP BY c.id',
 'Busca informações de um cliente'),

('produto', 'produto {nome}',
 'SELECT p.descricao, p.codigo, COUNT(nfi.id) as total_notas FROM produto p LEFT JOIN nota_fiscal_item nfi ON p.id = nfi.produto_id WHERE p.descricao ILIKE ''%{nome}%'' GROUP BY p.id',
 'Busca informações de um produto'),

('cenario', 'cenarios {status}',
 'SELECT ''icms'' as tipo, status, COUNT(*) as total FROM cenario_icms WHERE status = ''{status}'' UNION ALL SELECT ''ipi'' as tipo, status, COUNT(*) as total FROM cenario_ipi WHERE status = ''{status}'' UNION ALL SELECT ''pis'' as tipo, status, COUNT(*) as total FROM cenario_pis WHERE status = ''{status}''',
 'Busca cenários por status'),

('estatistica', 'resumo mes',
 'SELECT COUNT(DISTINCT nfi.numero_nf) as total_notas, COUNT(DISTINCT nfi.empresa_id) as total_empresas, SUM(nfi.valor_total) as valor_total FROM nota_fiscal_item nfi WHERE EXTRACT(MONTH FROM nfi.data_emissao) = EXTRACT(MONTH FROM CURRENT_DATE) AND EXTRACT(YEAR FROM nfi.data_emissao) = EXTRACT(YEAR FROM CURRENT_DATE)',
 'Resumo estatístico do mês atual');

-- Comentários nas tabelas
COMMENT ON TABLE chatbot_conversas IS 'Histórico de conversas do chatbot IA';
COMMENT ON TABLE chatbot_templates IS 'Templates de perguntas e consultas SQL para o chatbot';

-- Mensagem de confirmação
DO $$
BEGIN
    RAISE NOTICE 'CHATBOT IA CONFIGURADO COM SUCESSO!';
    RAISE NOTICE 'Tabelas criadas: chatbot_conversas, chatbot_templates';
    RAISE NOTICE 'Templates básicos inseridos: % registros', (SELECT COUNT(*) FROM chatbot_templates);
    RAISE NOTICE 'Sistema pronto para uso!';
END $$;
