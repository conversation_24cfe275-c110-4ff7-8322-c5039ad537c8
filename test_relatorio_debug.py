#!/usr/bin/env python3
"""
Teste de debug para o relatório geral reformulado
"""

import sys
import os

# Adicionar o diretório back ao path
sys.path.append(os.path.join(os.path.dirname(__file__), 'back'))

def test_relatorio_simples():
    """Testa a geração do relatório com dados mínimos"""
    try:
        # Imports necessários
        from reportlab.lib.pagesizes import A4
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
        from reportlab.lib.styles import getSampleStyleSheet
        from reportlab.lib.units import mm
        from reportlab.lib import colors
        from reportlab.lib.enums import TA_CENTER
        from reportlab.lib.colors import HexColor
        from io import BytesIO
        
        print("✅ Imports do ReportLab funcionando")
        
        # Criar um PDF simples para testar
        buffer = BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4,
                              leftMargin=20*mm, rightMargin=20*mm,
                              topMargin=20*mm, bottomMargin=20*mm)
        
        styles = getSampleStyleSheet()
        story = []
        
        # Testar HexColor
        try:
            cor_teste = HexColor('#6f42c1')
            print("✅ HexColor funcionando")
        except Exception as e:
            print(f"❌ Erro com HexColor: {e}")
            return False
        
        # Adicionar conteúdo simples
        story.append(Paragraph("TESTE DE RELATÓRIO", styles['Title']))
        story.append(Spacer(1, 20))
        story.append(Paragraph("Este é um teste simples do sistema de relatórios.", styles['Normal']))
        
        # Tentar gerar o PDF
        doc.build(story)
        buffer.seek(0)
        
        print("✅ PDF gerado com sucesso")
        print(f"Tamanho do buffer: {len(buffer.getvalue())} bytes")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro no teste de relatório: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_imports_modelos():
    """Testa se os imports dos modelos funcionam"""
    try:
        from models import Escritorio, Usuario, Empresa, AuditoriaSumario
        print("✅ Imports dos modelos funcionando")
        
        # Testar se o campo cor_relatorio existe
        if hasattr(Escritorio, 'cor_relatorio'):
            print("✅ Campo cor_relatorio encontrado no modelo Escritorio")
        else:
            print("❌ Campo cor_relatorio NÃO encontrado no modelo Escritorio")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Erro nos imports dos modelos: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_funcao_relatorio():
    """Testa a função de relatório com dados fictícios"""
    try:
        from routes.relatorio_routes import _criar_relatorio_geral_content
        
        # Criar objetos fictícios
        class EscritorioFake:
            def __init__(self):
                self.nome = "Escritório Teste"
                self.cnpj = "12.345.678/0001-90"
                self.responsavel = "João Silva"
                self.logo_path = None
                self.cor_relatorio = "#6f42c1"
        
        class EmpresaFake:
            def __init__(self):
                self.id = 1
                self.razao_social = "Empresa Teste LTDA"
                self.cnpj = "98.765.432/0001-10"
        
        escritorio = EscritorioFake()
        empresa = EmpresaFake()
        resultados_por_tributo = {}
        year = 2024
        month = 9
        status = 'todos'
        usuario_nome = "Usuário Teste"
        
        # Tentar criar o conteúdo do relatório
        story = _criar_relatorio_geral_content(
            escritorio, empresa, resultados_por_tributo, 
            year, month, status, usuario_nome
        )
        
        if story:
            print("✅ Função _criar_relatorio_geral_content funcionando")
            print(f"Story contém {len(story)} elementos")
            return True
        else:
            print("❌ Função retornou story vazia")
            return False
        
    except Exception as e:
        print(f"❌ Erro na função de relatório: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Função principal de teste"""
    print("🧪 Iniciando testes de debug do relatório...")
    
    resultados = []
    
    # Executar testes
    resultados.append(("Relatório Simples", test_relatorio_simples()))
    resultados.append(("Imports Modelos", test_imports_modelos()))
    resultados.append(("Função Relatório", test_funcao_relatorio()))
    
    # Resumo dos resultados
    print("\n" + "="*50)
    print("📊 RESUMO DOS TESTES DE DEBUG")
    print("="*50)
    
    sucessos = 0
    for nome, resultado in resultados:
        status = "✅ PASSOU" if resultado else "❌ FALHOU"
        print(f"{nome}: {status}")
        if resultado:
            sucessos += 1
    
    print(f"\nTotal: {sucessos}/{len(resultados)} testes passaram")
    
    if sucessos == len(resultados):
        print("🎉 Todos os testes passaram! O problema pode estar na validação de auditoria.")
    else:
        print("⚠️  Alguns testes falharam. Verifique os erros acima.")

if __name__ == "__main__":
    main()
