# Relatório Geral Reformulado - Sistema de Auditoria Fiscal

## 📋 Resumo das Implementações

O relatório geral de todos os tributos foi completamente reformulado conforme suas especificações. Agora o sistema possui:

### ✅ Funcionalidades Implementadas

1. **Campo de Cor Personalizada**
   - Adicionado campo `cor_relatorio` na tabela `escritorio`
   - Seletor de cor na página "Meu Perfil"
   - <PERSON><PERSON> <PERSON><PERSON><PERSON>: `#6f42c1` (roxo)

2. **Validação de Auditoria Completa**
   - Sistema só permite download quando empresa está 100% auditada
   - Considera tributos marcados como "não aplicável"
   - Cálculo correto de operações tributárias

3. **Novo Design do Relatório**
   - **Primeira página**: Logo, título, subtítulo, mês/ano, empresa, preparado por
   - **Segunda página**: Resumo geral com dados analisados e operações tributárias

## 🚀 Como Usar

### 1. Configurar Cor dos Relatórios

1. Acesse **Dashboard** → **Meu Perfil** (ícone do usuário no header)
2. Na seção "Informações do Escritório", encontre o campo **"Cor dos Relatórios"**
3. Clique no seletor de cor e escolha sua cor preferida
4. Clique em **"Salvar Alterações"**

### 2. Gerar Relatório Geral

1. Certifique-se que a empresa está **100% auditada** (todos os tributos aplicáveis)
2. Acesse **Dashboard** → **Empresa** (ou detalhes da empresa)
3. Clique no botão **"Relatório Geral"**
4. O sistema validará se a auditoria está completa
5. Se aprovado, o PDF será gerado e baixado automaticamente

### 3. Marcar Tributos como Não Aplicável

Se algum tributo não se aplica à empresa:

1. Acesse os detalhes da empresa no dashboard
2. Nos cards dos tributos, clique em **"Marcar como Não Aplicável"**
3. Informe o motivo (ex: "Empresa do Simples Nacional - IPI não aplicável")
4. O tributo será considerado como "auditado" para fins do relatório

## 📄 Estrutura do Novo Relatório

### Primeira Página
- **Logo do escritório** (alinhado à direita)
- **Título**: "RELATÓRIO DE AUDITORIA FISCAL" (preto, maiúsculo)
- **Subtítulo**: "Movimentação de Entradas e Saídas de Notas Fiscais" (cor personalizada)
- **Período**: "SETEMBRO DE 2025" (cor personalizada)
- **Empresa**: Nome da empresa (preto, maiúsculo)
- **Preparado por**: Nome do usuário que baixou (cor personalizada + preto)

### Segunda Página
- **Logo do escritório** (alinhado à direita)
- **Título**: "RESUMO GERAL" (preto, maiúsculo)

#### Seção: DADOS ANALISADOS
- **3 cards lado a lado**:
  - Total de Notas Fiscais
  - Total de Produtos
  - Total de Operações Tributárias

#### Seção: OPERAÇÕES TRIBUTÁRIAS
- **6 cards dos tributos** (3 por linha, 2 linhas):
  - Nome do tributo (cor personalizada)
  - Quantidade conforme e inconsistente
  - Valor inconsistente (vermelho se > 0)

#### Seção: TOTAIS
- **Total das Inconsistências Apuradas** (centralizado, vermelho)
- **Pagando Mais Imposto** e **Pagando Menos Imposto** (lado a lado, vermelho)

## 🔧 Arquivos Modificados

### Backend
- `back/models/escritorio.py` - Adicionado campo `cor_relatorio`
- `back/routes/perfil_routes.py` - API para salvar cor
- `back/routes/relatorio_routes.py` - Reformulação completa do relatório
- `back/services/auditoria_service.py` - Validação de auditoria completa

### Frontend
- `front/static/js/perfil_dashboard.js` - Seletor de cor

### Database
- `db/migration_add_cor_relatorio.sql` - Migração para adicionar campo

## ⚠️ Requisitos

### Para Gerar o Relatório:
1. **Empresa 100% auditada**: Todos os tributos aplicáveis devem estar auditados
2. **Tributos não aplicáveis**: Devem ser marcados como "não aplicável" com motivo
3. **Dados válidos**: Empresa deve ter dados de auditoria no período selecionado

### Cálculo de Operações Tributárias:
- **Fórmula**: Soma dos produtos auditados para cada tributo aplicável
- **Tributos não aplicáveis**: Não contam para o total
- **Exemplo**: Se empresa tem 100 produtos e 4 tributos aplicáveis = 400 operações

## 🎨 Personalização

A cor escolhida no perfil será usada em:
- Subtítulos da primeira página
- Período (mês/ano)
- "Preparado por"
- Títulos das seções na segunda página
- Nomes dos tributos nos cards

## 📞 Suporte

Se encontrar algum problema:
1. Verifique se a migração do banco foi executada
2. Confirme que todos os tributos estão auditados ou marcados como não aplicável
3. Teste com uma empresa que tenha dados de auditoria completos
