#!/usr/bin/env python3
"""
Teste para verificar a reformulação do relatório geral
"""

import sys
import os

# Adicionar o diretório back ao path
sys.path.append(os.path.join(os.path.dirname(__file__), 'back'))

def test_validacao_auditoria():
    """Testa a função de validação de auditoria completa"""
    try:
        from services.auditoria_service import AuditoriaService
        
        # Teste com empresa fictícia
        empresa_id = 1
        year = 2024
        month = 9
        
        resultado = AuditoriaService.validar_empresa_auditoria_completa(empresa_id, year, month)
        
        print("=== Teste de Validação de Auditoria ===")
        print(f"Empresa ID: {empresa_id}")
        print(f"Período: {month:02d}/{year}")
        print(f"Auditoria completa: {resultado['auditoria_completa']}")
        print(f"Tributos aplicáveis: {resultado['tributos_aplicaveis']}")
        print(f"Tributos auditados: {resultado['tributos_auditados']}")
        print(f"Tributos pendentes: {resultado['tributos_pendentes']}")
        print(f"Total operações tributárias: {resultado['total_operacoes_tributarias']}")
        print(f"Detalhes: {resultado['detalhes']}")
        
        return True
        
    except Exception as e:
        print(f"Erro no teste de validação: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_modelo_escritorio():
    """Testa se o modelo Escritorio tem o campo cor_relatorio"""
    try:
        from models import Escritorio
        
        print("\n=== Teste do Modelo Escritorio ===")
        
        # Verificar se o campo existe no modelo
        if hasattr(Escritorio, 'cor_relatorio'):
            print("✅ Campo cor_relatorio encontrado no modelo")
        else:
            print("❌ Campo cor_relatorio NÃO encontrado no modelo")
            return False
        
        # Verificar método to_dict
        escritorio_dict = {
            'id': 1,
            'nome': 'Teste',
            'cnpj': '12345678000100',
            'endereco': 'Teste',
            'responsavel': 'Teste',
            'logo_path': None,
            'cor_relatorio': '#6f42c1'
        }
        
        print(f"Estrutura esperada do to_dict: {list(escritorio_dict.keys())}")
        
        return True
        
    except Exception as e:
        print(f"Erro no teste do modelo: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_imports():
    """Testa se todos os imports necessários funcionam"""
    try:
        print("\n=== Teste de Imports ===")
        
        # Testar imports do relatório
        from reportlab.platypus import Paragraph, Spacer, Table, TableStyle, Image, PageBreak
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import mm
        from reportlab.lib import colors
        from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT
        from reportlab.lib.colors import HexColor
        print("✅ Imports do ReportLab funcionando")
        
        # Testar imports dos modelos
        from models import Escritorio, Usuario, Empresa, AuditoriaSumario
        print("✅ Imports dos modelos funcionando")
        
        # Testar imports dos serviços
        from services.auditoria_service import AuditoriaService
        print("✅ Imports dos serviços funcionando")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro nos imports: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Função principal de teste"""
    print("🧪 Iniciando testes da reformulação do relatório geral...")
    
    resultados = []
    
    # Executar testes
    resultados.append(("Imports", test_imports()))
    resultados.append(("Modelo Escritorio", test_modelo_escritorio()))
    resultados.append(("Validação Auditoria", test_validacao_auditoria()))
    
    # Resumo dos resultados
    print("\n" + "="*50)
    print("📊 RESUMO DOS TESTES")
    print("="*50)
    
    sucessos = 0
    for nome, resultado in resultados:
        status = "✅ PASSOU" if resultado else "❌ FALHOU"
        print(f"{nome}: {status}")
        if resultado:
            sucessos += 1
    
    print(f"\nTotal: {sucessos}/{len(resultados)} testes passaram")
    
    if sucessos == len(resultados):
        print("🎉 Todos os testes passaram! O sistema está pronto.")
    else:
        print("⚠️  Alguns testes falharam. Verifique os erros acima.")

if __name__ == "__main__":
    main()
