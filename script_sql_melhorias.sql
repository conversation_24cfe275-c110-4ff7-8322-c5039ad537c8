-- Script SQL para implementar melhorias no sistema de Auditoria Fiscal
-- Execute este script no PgAdmin para adicionar os campos necessários

-- 1. Adicionar campos responsavel e logo_path na tabela escritorio
ALTER TABLE escritorio ADD COLUMN IF NOT EXISTS responsavel VARCHAR(255);
ALTER TABLE escritorio ADD COLUMN IF NOT EXISTS logo_path VARCHAR(500);

-- Adicionar comentários para documentação
COMMENT ON COLUMN escritorio.responsavel IS 'Nome do responsável pelo escritório';
COMMENT ON COLUMN escritorio.logo_path IS 'Caminho para o arquivo de logo/emblema do escritório';

-- 2. Criar diretório para uploads (isso deve ser feito no sistema de arquivos)
-- mkdir -p front/static/uploads/logos

-- 3. Verificar se as tabelas necessárias existem e têm os campos corretos
-- Verificar estrutura da tabela empresa
DO $$
BEGIN
    -- Verificar se o campo razao_social existe na tabela empresa
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'empresa' AND column_name = 'razao_social'
    ) THEN
        -- Se não existir, adicionar o campo
        ALTER TABLE empresa ADD COLUMN razao_social VARCHAR(255);

        -- Copiar dados do campo 'nome' se existir
        IF EXISTS (
            SELECT 1 FROM information_schema.columns
            WHERE table_name = 'empresa' AND column_name = 'nome'
        ) THEN
            UPDATE empresa SET razao_social = nome WHERE razao_social IS NULL;
        END IF;
    END IF;
END $$;

-- 4. Verificar e corrigir dados existentes
-- Atualizar empresas que não têm razao_social preenchida
UPDATE empresa
SET razao_social = COALESCE(nome_fantasia, nome, 'Empresa sem nome')
WHERE razao_social IS NULL OR razao_social = '';

-- 5. Verificar estrutura das tabelas de auditoria
-- Verificar se a tabela auditoria_resultado tem todos os campos necessários
DO $$
BEGIN
    -- Verificar campos de base de cálculo
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'auditoria_resultado' AND column_name = 'base_calculo_nota'
    ) THEN
        ALTER TABLE auditoria_resultado ADD COLUMN base_calculo_nota DECIMAL(15, 2);
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'auditoria_resultado' AND column_name = 'base_calculo_calculada'
    ) THEN
        ALTER TABLE auditoria_resultado ADD COLUMN base_calculo_calculada DECIMAL(15, 2);
    END IF;

    -- Verificar campos de alíquota
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'auditoria_resultado' AND column_name = 'aliquota_nota'
    ) THEN
        ALTER TABLE auditoria_resultado ADD COLUMN aliquota_nota DECIMAL(10, 4);
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'auditoria_resultado' AND column_name = 'aliquota_cenario'
    ) THEN
        ALTER TABLE auditoria_resultado ADD COLUMN aliquota_cenario DECIMAL(10, 4);
    END IF;

    -- Verificar campos de CST
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'auditoria_resultado' AND column_name = 'cst_nota'
    ) THEN
        ALTER TABLE auditoria_resultado ADD COLUMN cst_nota VARCHAR(3);
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'auditoria_resultado' AND column_name = 'cst_calculada'
    ) THEN
        ALTER TABLE auditoria_resultado ADD COLUMN cst_calculada VARCHAR(3);
    END IF;
END $$;

-- 6. Verificar se a tabela nota_fiscal_item existe e tem os campos necessários
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'nota_fiscal_item') THEN
        CREATE TABLE nota_fiscal_item (
            id SERIAL PRIMARY KEY,
            empresa_id INTEGER REFERENCES empresa(id),
            escritorio_id INTEGER REFERENCES escritorio(id),
            numero_nf VARCHAR(20),
            chave_nf VARCHAR(50),
            data_emissao DATE,
            cnpj_emitente VARCHAR(18),
            razao_social_emitente VARCHAR(255),
            cliente_id INTEGER REFERENCES cliente(id),
            produto_id INTEGER REFERENCES produto(id),
            cfop VARCHAR(4),
            ncm VARCHAR(10),
            cst_icms VARCHAR(3),
            cst_ipi VARCHAR(3),
            cst_pis VARCHAR(3),
            cst_cofins VARCHAR(3),
            valor_produto DECIMAL(15, 2),
            valor_frete DECIMAL(15, 2),
            valor_desconto DECIMAL(15, 2),
            base_calculo_icms DECIMAL(15, 2),
            aliquota_icms DECIMAL(10, 4),
            valor_icms DECIMAL(15, 2),
            base_calculo_icms_st DECIMAL(15, 2),
            aliquota_icms_st DECIMAL(10, 4),
            valor_icms_st DECIMAL(15, 2),
            base_calculo_ipi DECIMAL(15, 2),
            aliquota_ipi DECIMAL(10, 4),
            valor_ipi DECIMAL(15, 2),
            base_calculo_pis DECIMAL(15, 2),
            aliquota_pis DECIMAL(10, 4),
            valor_pis DECIMAL(15, 2),
            base_calculo_cofins DECIMAL(15, 2),
            aliquota_cofins DECIMAL(10, 4),
            valor_cofins DECIMAL(15, 2),
            data_importacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE (empresa_id, cliente_id, produto_id, data_emissao, numero_nf)
        );
    END IF;
END $$;

-- 7. Verificar permissões e índices
-- Criar índices para melhorar performance das consultas de relatório
CREATE INDEX IF NOT EXISTS idx_auditoria_resultado_empresa_tipo ON auditoria_resultado(empresa_id, tipo_tributo);
CREATE INDEX IF NOT EXISTS idx_auditoria_resultado_status ON auditoria_resultado(status);
CREATE INDEX IF NOT EXISTS idx_nota_fiscal_item_empresa ON nota_fiscal_item(empresa_id);
CREATE INDEX IF NOT EXISTS idx_tributo_data_emissao ON tributo(data_emissao);

-- 8. Atualizar dados de exemplo (opcional)
-- Inserir um responsável padrão para escritórios que não têm
UPDATE escritorio
SET responsavel = 'Responsável não informado'
WHERE responsavel IS NULL OR responsavel = '';

-- 9. Verificar integridade dos dados
-- Verificar se há empresas sem escritório_id
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM empresa WHERE escritorio_id IS NULL) THEN
        RAISE NOTICE 'ATENÇÃO: Existem empresas sem escritório_id definido. Isso pode causar problemas nos relatórios.';
    END IF;
END $$;

-- 10. Criar função para validar dados antes dos relatórios
CREATE OR REPLACE FUNCTION validar_dados_relatorio(p_empresa_id INTEGER)
RETURNS TABLE(
    campo VARCHAR(50),
    problema TEXT
) AS $$
BEGIN
    -- Verificar se empresa existe
    IF NOT EXISTS (SELECT 1 FROM empresa WHERE id = p_empresa_id) THEN
        RETURN QUERY SELECT 'empresa'::VARCHAR(50), 'Empresa não encontrada'::TEXT;
        RETURN;
    END IF;

    -- Verificar se empresa tem escritório
    IF NOT EXISTS (
        SELECT 1 FROM empresa e
        JOIN escritorio esc ON e.escritorio_id = esc.id
        WHERE e.id = p_empresa_id
    ) THEN
        RETURN QUERY SELECT 'escritorio'::VARCHAR(50), 'Empresa não está vinculada a um escritório'::TEXT;
    END IF;

    -- Verificar se escritório tem responsável
    IF EXISTS (
        SELECT 1 FROM empresa e
        JOIN escritorio esc ON e.escritorio_id = esc.id
        WHERE e.id = p_empresa_id AND (esc.responsavel IS NULL OR esc.responsavel = '')
    ) THEN
        RETURN QUERY SELECT 'responsavel'::VARCHAR(50), 'Escritório não tem responsável definido'::TEXT;
    END IF;

    -- Se chegou até aqui, dados estão OK
    IF NOT FOUND THEN
        RETURN QUERY SELECT 'ok'::VARCHAR(50), 'Dados válidos para geração de relatório'::TEXT;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Exemplo de uso da função de validação:
-- SELECT * FROM validar_dados_relatorio(1);

COMMIT;

-- Mensagem final
DO $$
BEGIN
    RAISE NOTICE '=== SCRIPT EXECUTADO COM SUCESSO ===';
    RAISE NOTICE 'Campos adicionados:';
    RAISE NOTICE '- escritorio.responsavel (VARCHAR(255))';
    RAISE NOTICE '- escritorio.logo_path (VARCHAR(500))';
    RAISE NOTICE '';
    RAISE NOTICE 'Próximos passos:';
    RAISE NOTICE '1. Criar diretório: front/static/uploads/logos';
    RAISE NOTICE '2. Configurar permissões de escrita no diretório';
    RAISE NOTICE '3. Testar upload de logo na interface';
    RAISE NOTICE '4. Testar geração de relatórios';
    RAISE NOTICE '';
    RAISE NOTICE 'Para validar dados de uma empresa antes de gerar relatório:';
    RAISE NOTICE 'SELECT * FROM validar_dados_relatorio(ID_DA_EMPRESA);';
END $$;
