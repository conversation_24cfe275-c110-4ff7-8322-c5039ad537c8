#!/usr/bin/env python3
"""
Script para atualizar os métodos de auditoria restantes com cenario_status
"""

import re

def update_auditoria_service():
    """Atualiza o arquivo auditoria_service.py com as mudanças necessárias"""
    
    file_path = 'back/services/auditoria_service.py'
    
    # Ler o arquivo
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Padrões para atualizar
    updates = [
        # _auditar_icms_st
        {
            'old': r'cenario = self\._buscar_cenario_vigente\(\'icms_st\', tributo\)',
            'new': 'cenario, cenario_status = self._buscar_cenario_vigente(\'icms_st\', tributo)'
        },
        {
            'old': r'resultado_existente\.status = status\n(\s+)resultado_existente\.data_auditoria = datetime\.now\(\)',
            'new': r'resultado_existente.status = status\n\1resultado_existente.cenario_status = cenario_status\n\1resultado_existente.data_auditoria = datetime.now()'
        },
        {
            'old': r'status=status,\n(\s+)data_auditoria=datetime\.now\(\)\n(\s+)\)\n(\s+)db\.session\.add\(resultado\)',
            'new': r'status=status,\n\1cenario_status=cenario_status,\n\1data_auditoria=datetime.now()\n\2)\n\3db.session.add(resultado)'
        },
        
        # _auditar_pis
        {
            'old': r'cenario = self\._buscar_cenario_vigente\(\'pis\', tributo\)',
            'new': 'cenario, cenario_status = self._buscar_cenario_vigente(\'pis\', tributo)'
        },
        
        # _auditar_cofins
        {
            'old': r'cenario = self\._buscar_cenario_vigente\(\'cofins\', tributo\)',
            'new': 'cenario, cenario_status = self._buscar_cenario_vigente(\'cofins\', tributo)'
        },
        
        # _auditar_difal
        {
            'old': r'cenario = self\._buscar_cenario_vigente\(\'difal\', tributo\)',
            'new': 'cenario, cenario_status = self._buscar_cenario_vigente(\'difal\', tributo)'
        }
    ]
    
    # Aplicar as atualizações
    for update in updates:
        content = re.sub(update['old'], update['new'], content)
    
    # Escrever o arquivo atualizado
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("Arquivo auditoria_service.py atualizado com sucesso!")

if __name__ == '__main__':
    update_auditoria_service()
