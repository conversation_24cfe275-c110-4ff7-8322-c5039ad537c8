/*
 * Dashboard Empresa CSS - Estilos específicos para o dashboard de empresa
 * Inclui correções para o modo escuro
 */

/* Estilo para o texto de ajuda abaixo do título de tributos */
.tributos-help-text {
  color: var(--gray-600);
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

/* Estilo para o card de relatório geral */
.relatorio-geral-card {
  border: 1px solid var(--border-color);
  background-color: var(--card-bg);
  transition: all 0.3s ease;
  color: var(--text-color);
}

.relatorio-geral-card .card-header {
  background: var(--primary) !important;
  border-bottom: 1px solid var(--border-color);
  color: white;
}

.relatorio-geral-card .card-body {
  background-color: var(--card-bg);
  color: var(--text-color);
}

.relatorio-geral-card .text-muted {
  color: var(--text-muted) !important;
}

.relatorio-geral-card .card-text {
  color: var(--text-color);
  margin-bottom: 0.5rem;
}

.relatorio-geral-card .small {
  color: var(--text-muted);
}

/* Estilos específicos para modo escuro */
body.dark-theme .tributos-help-text {
  color: var(--dark-text-secondary);
}

body.dark-theme .relatorio-geral-card {
  border-color: var(--dark-border-primary);
  background-color: var(--dark-bg-secondary);
  color: var(--dark-text-primary);
}

body.dark-theme .relatorio-geral-card .card-body {
  background-color: var(--dark-bg-secondary);
  color: var(--dark-text-primary);
}

body.dark-theme .relatorio-geral-card .text-muted,
body.dark-theme .relatorio-geral-card .small {
  color: var(--dark-text-muted) !important;
}

body.dark-theme .relatorio-geral-card .card-text {
  color: var(--dark-text-primary);
}
